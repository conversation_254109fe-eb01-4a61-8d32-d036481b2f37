# Tomodata

Tomodata est un outil permettant de récupérer automatiquement vos données personnelles stockées derrière des authentifications sur divers services web français.

## 🚀 Phase 1 - MVP

<PERSON>tte première version (MVP) fournit les fonctionnalités de base pour le service Ameli (Assurance Maladie) :

- ✅ Navigation automatique vers les sites de services
- ✅ Gestion des sessions utilisateur (cookies sauvegardés)
- ✅ Assistance pour le téléchargement manuel
- ✅ Interface en ligne de commande simple
- ✅ Organisation basique des fichiers

## Installation

```bash
# Cloner le projet
git clone <repository-url>
cd tomodata

# Installer les dépendances
npm install

# Installer les navigateurs Playwright
npx playwright install

# Compiler le projet
npm run build

# Rendre le script exécutable
chmod +x bin/tomodata
```

## Utilisation

### Commandes disponibles

```bash
# Lister les services disponibles
npm run dev list

# Obtenir des informations sur un service
npm run dev info ameli

# Lancer l'extraction (mode visible recommandé pour la première fois)
npm run dev run ameli --debug --headless false

# Lancer l'extraction en mode normal
npm run dev run ameli

# Vider la session sauvegardée
npm run dev clear-session ameli
```

### Premier lancement

1. **Lancez en mode debug** pour voir le navigateur :
   ```bash
   npm run dev run ameli --debug --headless false
   ```

2. **Connectez-vous manuellement** avec vos identifiants Ameli

3. **L'outil navigue automatiquement** vers la section documents

4. **Téléchargez manuellement** les documents souhaités

5. **Votre session est sauvegardée** pour les prochaines utilisations

## Architecture

Le projet suit une architecture modulaire :

```
src/
├── core/           # Orchestrateur principal
├── connectors/     # Connecteurs pour chaque service
│   ├── base/      # Classes de base
│   └── ameli/     # Connecteur Ameli
├── session/       # Gestion des sessions
├── cli/           # Interface ligne de commande
├── utils/         # Utilitaires (logger, etc.)
└── types/         # Types TypeScript
```

## Configuration

Les données sont stockées dans `tomodata-data/` :

```
tomodata-data/
├── config/         # Configuration utilisateur
├── sessions/       # Sessions sauvegardées
├── downloads/      # Fichiers téléchargés
└── logs/          # Logs de l'application
```

## Services supportés

- **Ameli** (Assurance Maladie) - ✅ MVP
- Autres services à venir dans les prochaines phases

## Développement

```bash
# Développement avec rechargement automatique
npm run dev <command>

# Tests
npm test

# Linter
npm run lint

# Build de production
npm run build
```

## Roadmap

### Phase 2 (6 semaines)
- Chiffrement des identifiants stockés
- 2-3 nouveaux connecteurs
- Amélioration de l'interface

### Phase 3 (8 semaines)
- Automatisation complète des téléchargements
- Mode développeur avancé
- API pour connecteurs personnalisés

### Phase 4 (4 semaines)
- Intégrations externes (Cozy Cloud, PocketBase)
- Interface web
- Optimisations

## Contribuer

1. Fork le projet
2. Créez une branche pour votre fonctionnalité
3. Committez vos changements
4. Poussez vers la branche
5. Ouvrez une Pull Request

## Sécurité

- Toutes les données sont stockées localement
- Les sessions sont chiffrées
- Respect du RGPD
- Utilisation uniquement pour vos propres données

## Support

Pour signaler des problèmes ou demander de l'aide :
- Créez une issue sur GitHub
- Consultez le fichier QUICKSTART.md
- Vérifiez la section Dépannage

## Licence

MIT License - voir le fichier LICENSE pour plus de détails.
