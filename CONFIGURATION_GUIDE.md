# Guide de Configuration des Connecteurs

## Vue d'ensemble

Les connecteurs Tomodata utilisent maintenant un système de configuration unifié basé sur des fichiers `config.json`. Cette approche élimine la duplication de code et permet de configurer les connecteurs sans modifier le code TypeScript.

## Structure de Configuration

### Emplacement
Chaque connecteur a son propre fichier `config.json` dans son dossier :
```
src/connectors/
├── ameli/
│   ├── config.json          # Configuration Ameli
│   └── AmeliConnector.ts     # Code du connecteur
├── template/
│   ├── config.json          # Configuration Template
│   └── TemplateConnector.ts  # Code du connecteur
```

### Chargement automatique
La classe `BaseConnector` charge automatiquement le `config.json` correspondant :

```typescript
// Dans le connecteur, plus besoin de getConfiguration()
export class AmeliConnector extends BaseConnector {
  name = 'ameli';

  // La configuration est automatiquement chargée depuis config.json
  // config.json DOIT exister - sinon une erreur claire est levée
}
```

**Important :** Le fichier `config.json` est **obligatoire**. Si il n'existe pas, le système lèvera une erreur claire :
```
Configuration file not found: /path/to/connectors/ameli/config.json.
Please create a config.json file for the ameli connector.
```

## Exemple de Configuration Complète

### config.json pour Ameli
```json
{
  "name": "ameli",
  "displayName": "Assurance Maladie",
  "baseUrl": "https://www.ameli.fr",
  "selectors": {
    "username": "#username",
    "password": "#password",
    "loginButton": "#kc-login",
    "downloadButton": "button:has-text('Télécharger'), a[download]",
    "documentsLink": "a[href*='attestation'], a[href*='documents']"
  },
  "downloadPatterns": ["*.pdf", "*.csv", "*.zip"],
  "fileNaming": "{date}_ameli_{type}_{reference}.{ext}",
  "navigation": {
    "documentsUrl": "/assure/documents",
    "attestationsUrl": "/assure/mes-attestations",
    "compteUrl": "/assure/compte-ameli"
  },
  "timeouts": {
    "navigation": 30000,
    "download": 60000,
    "authentication": 300000
  },
  "features": {
    "autoDownload": true,
    "sessionPersistence": true,
    "duplicateDetection": true
  }
}
```

## Propriétés de Configuration

### Propriétés obligatoires
- `name` : Identifiant unique du connecteur
- `displayName` : Nom affiché à l'utilisateur
- `baseUrl` : URL de base du service
- `selectors.username` : Sélecteur CSS pour le champ nom d'utilisateur
- `selectors.password` : Sélecteur CSS pour le champ mot de passe
- `selectors.loginButton` : Sélecteur CSS pour le bouton de connexion
- `downloadPatterns` : Patterns de fichiers à télécharger
- `fileNaming` : Pattern de nommage des fichiers

**Note:** La logique d'authentification (URL de connexion, étapes spécifiques) est maintenant gérée directement par chaque connecteur dans sa méthode `authenticate()`, permettant plus de flexibilité pour les cas complexes.

### Propriétés optionnelles étendues
- `navigation` : URLs et sélecteurs de navigation
- `timeouts` : Délais d'attente personnalisés
- `features` : Fonctionnalités activées/désactivées
- `selectors.*` : Sélecteurs CSS supplémentaires
- Toute propriété personnalisée selon les besoins

## Utilisation dans le Code

### Accès à la configuration
```typescript
export class AmeliConnector extends BaseConnector {
  async extractData(): Promise<DataExtractionResult> {
    const config = this.getConfiguration();

    // Accès aux propriétés standard
    console.log(config.loginUrl);
    console.log(config.selectors.username);

    // Accès aux propriétés étendues
    const navigation = (config as any).navigation;
    const timeouts = (config as any).timeouts;
    const features = (config as any).features;

    if (navigation?.documentsUrl) {
      await this.page.goto(config.baseUrl + navigation.documentsUrl);
    }
  }
}
```

## Avantages

### 1. Élimination des duplications
❌ **Avant** : Configuration dupliquée dans config.json ET dans getConfiguration()
✅ **Maintenant** : Configuration unique dans config.json

### 2. Facilité de maintenance
- Modification des sélecteurs sans recompilation
- Ajout de nouvelles propriétés sans modification du code
- Configuration partageable entre environnements

### 3. Extensibilité
- Ajout de propriétés personnalisées selon les besoins
- Support TypeScript avec casting quand nécessaire
- Validation automatique des propriétés obligatoires

### 4. Facilité de développement
- Nouveau connecteur = nouveau dossier + config.json + classe minimale
- Tests automatiques de la configuration
- Debug facilité avec configuration visible

## Création d'un Nouveau Connecteur

### 1. Créer le dossier et la configuration
```bash
mkdir src/connectors/mon-service
```

### 2. Créer config.json
```json
{
  "name": "mon-service",
  "displayName": "Mon Service",
  "baseUrl": "https://mon-service.com",
  "loginUrl": "https://mon-service.com/login",
  "selectors": {
    "username": "#email",
    "password": "#password",
    "loginButton": ".login-button"
  },
  "downloadPatterns": ["*.pdf"],
  "fileNaming": "{date}_mon-service_{type}.{ext}"
}
```

### 3. Créer la classe connecteur
```typescript
import { BaseConnector } from '../base/BaseConnector';
import { ConnectorConfiguration, DataExtractionResult } from '../../types';

export class MonServiceConnector extends BaseConnector {
  name = 'mon-service';
  displayName = 'Mon Service';
  version = '1.0.0';

  constructor(logger: Logger) {
    super(logger);
  }

  async extractData(): Promise<DataExtractionResult> {
    const config = this.getConfiguration();
    // Implémentation spécifique...
    // Le config.json est automatiquement chargé !
  }
}
```

### 4. Enregistrer dans TomodataCore
```typescript
this.registerConnector('mon-service', () => new MonServiceConnector(this.logger));
```

C'est tout ! Le connecteur est prêt à utiliser sa configuration depuis config.json.
