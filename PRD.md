# Tomodata - Product Requirements Document

## Vue d'ensemble

Tomodata est un outil permettant aux utilisateurs de récupérer automatiquement leurs données personnelles stockées derrière des authentifications sur divers services web. Utilisant Node.js et Playwright, il offre une approche progressive allant d'une utilisation manuelle assistée à une automatisation complète.

## Objectifs

- **Objectif principal** : Faciliter la récupération de données personnelles depuis des services web authentifiés
- **Objectif secondaire** : Permettre l'automatisation progressive de ces processus
- **Objectif tertiaire** : Créer un écosystème de connecteurs partageables entre utilisateurs

## Personas utilisateurs

### Utilisateur novice
- Souhaite récupérer ses données manuellement avec assistance
- Préfère une interface simple et guidée
- Priorité à la sécurité et la transparence

### Utilisateur avancé
- Souhaite automatiser le processus
- Accepte de configurer des connecteurs spécifiques
- Intéressé par le mode développeur

### Développeur de connecteurs
- Crée des connecteurs pour de nouveaux services
- Utilise le mode développeur et l'API
- Partage ses connecteurs avec la communauté

## Fonctionnalités

### Version de base (MVP)

#### Fonctionnalités principales
1. **Navigation assistée**
   - Ouverture automatique du navigateur sur l'URL du service
   - Interface utilisateur pour saisir les identifiants manuellement
   - Assistance au téléchargement avec nommage suggéré

2. **Gestion de session**
   - Sauvegarde automatique des cookies et localStorage
   - Réutilisation de la session lors du prochain lancement
   - Gestion de l'expiration des sessions

3. **Organisation des fichiers**
   - Proposition de noms de fichiers appropriés
   - Possibilité pour l'utilisateur de personnaliser le nommage
   - Structure de dossiers basique

#### Services supportés initialement
- ameli.fr (Assurance Maladie)
- Extensible via configuration

### Améliorations spécifiques aux services (par ordre de priorité)

#### Niveau 1 - Configuration de base
- **URL de démarrage personnalisable**
- **Configuration par service** (format JSON)

#### Niveau 2 - Automatisation des identifiants
- **Interception et sauvegarde des identifiants**
  - Chiffrement des données sensibles
  - Réutilisation automatique lors des prochaines sessions
- **Gestion sécurisée des mots de passe**
  - Utilisation d'un master password
  - Stockage chiffré local

#### Niveau 3 - Automatisation des téléchargements
- **Détection automatique des nouveaux fichiers**
- **Téléchargement automatique**
  - Éviter les doublons
  - Vérification de l'intégrité
- **Nommage intelligent**
  - Basé sur le contenu et les métadonnées
  - Patterns configurables par service
- **Organisation automatique**
  - Arborescence logique (par année, type de document, etc.)
  - Règles personnalisables

#### Niveau 4 - Automatisation complète
- **Exécution programmée**
  - Cron jobs ou tâches planifiées
  - Fréquence configurable par service
- **Sauvegarde des données d'identité**
  - Export JSON des informations utilisateur
  - Données structurées réutilisables

### Améliorations transversales

#### Mode développeur
- **Interface de débogage**
  - Navigateur visible avec DevTools
  - Console intégrée pour tester les commandes
- **API de développement**
  - Fonctions de téléchargement avec paramètres
  - Utilitaires de sauvegarde d'identifiants
  - API commune pour tous les connecteurs

#### Intégrations externes
- **Services de stockage personnel**
  - Cozy Cloud
  - PocketBase
  - Alternative au stockage local

## Exigences techniques

### Technologies principales
- **Runtime** : Node.js (version LTS)
- **Automatisation navigateur** : Playwright
- **Stockage** : Système de fichiers local (extensible)
- **Chiffrement** : crypto (module Node.js natif)

### Sécurité
- Chiffrement des identifiants stockés
- Gestion sécurisée des sessions
- Validation des téléchargements
- Isolation des processus par service

### Performance
- Gestion efficace de la mémoire
- Téléchargements en parallèle (configurable)
- Cache intelligent des sessions

### Extensibilité
- Architecture modulaire pour les connecteurs
- API standardisée pour les développeurs
- Configuration déclarative (JSON/YAML)

## Architecture de données

### Structure des connecteurs
```json
{
  "name": "ameli",
  "displayName": "Assurance Maladie",
  "baseUrl": "https://www.ameli.fr",
  "loginUrl": "https://connexion.ameli.fr",
  "config": {
    "selectors": {
      "username": "#username",
      "password": "#password",
      "loginButton": "#login"
    },
    "downloadPatterns": ["*.pdf", "*.csv"],
    "fileNaming": "{date}_{type}_{reference}.{ext}"
  }
}
```

### Structure des données utilisateur
```json
{
  "user": {
    "id": "uuid",
    "preferences": {
      "downloadPath": "./downloads",
      "autoRename": true,
      "schedule": "daily"
    }
  },
  "services": {
    "ameli": {
      "enabled": true,
      "lastRun": "2024-01-01T00:00:00Z",
      "credentials": "encrypted_data"
    }
  }
}
```

## Critères de succès

### Critères fonctionnels
- Réduction du temps de récupération des données de 80%
- Taux de succès de téléchargement > 95%
- Zéro perte de données pendant le processus

### Critères non-fonctionnels
- Interface utilisateur intuitive (temps d'apprentissage < 5 min)
- Sécurité des données (audit de sécurité passé)
- Performance acceptable (< 30s par service)

## Contraintes et limitations

### Contraintes techniques
- Dépendance aux APIs et interfaces web des services tiers
- Limitations de Playwright selon les navigateurs
- Restrictions légales sur l'automatisation

### Contraintes légales
- Respect des conditions d'utilisation des services
- Protection des données personnelles (RGPD)
- Utilisation uniquement pour ses propres données

### Limitations connues
- Peut être affecté par les changements d'interface des services
- Nécessite une maintenance régulière des connecteurs
- Dépendant de la stabilité des services tiers

## Roadmap

### Phase 1 (MVP) - 4 semaines
- Implémentation de base avec ameli.fr
- Gestion des sessions
- Interface utilisateur simple

### Phase 2 - 6 semaines
- Ajout du chiffrement des identifiants
- 2-3 connecteurs supplémentaires
- Amélioration de l'interface

### Phase 3 - 8 semaines
- Automatisation complète
- Mode développeur
- API pour connecteurs

### Phase 4 - 4 semaines
- Intégrations externes (Cozy Cloud, PocketBase)
- Optimisations et stabilisation
