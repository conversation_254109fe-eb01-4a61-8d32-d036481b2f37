import { Logger } from 'winston';
import * as fs from 'fs-extra';
import * as path from 'path';
import { DataStorageConfiguration, SaveDataOptions, SaveDataResult, MergeOperationDetails, StoredDataStructure } from '../types';

// Base interface for all storage providers
interface IStorageProvider {
  initialize(): Promise<void>;
  saveData(doctype: string, data: unknown, options: SaveDataOptions): Promise<SaveDataResult>;
  cleanup(): Promise<void>;
}

// JSON file storage provider
class JsonStorageProvider implements IStorageProvider {
  private baseDirectory: string;
  private prettyPrint: boolean;
  private logger: Logger;

  constructor(config: DataStorageConfiguration, logger: Logger) {
    this.baseDirectory = config.options.baseDirectory || './tomodata-data/downloads';
    this.prettyPrint = config.options.prettyPrint !== false; // default to true
    this.logger = logger;
  }

  async initialize(): Promise<void> {
    await fs.ensureDir(this.baseDirectory);
    this.logger.debug(`JSON storage initialized at: ${this.baseDirectory}`);
  }

  async saveData(doctype: string, data: unknown, options: SaveDataOptions = {}): Promise<SaveDataResult> {
    try {
      // For merge operations, use a consistent filename
      const filename = options.primaryKey 
        ? `${doctype}.json`  // Use consistent filename for mergeable data
        : options.filename || `${doctype}-${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
      
      // Create subdirectory for the doctype
      const doctypeDir = path.join(this.baseDirectory, doctype);
      await fs.ensureDir(doctypeDir);
      
      const filePath = path.join(doctypeDir, filename);
      
      // Check if this is a merge operation
      if (options.primaryKey && await fs.pathExists(filePath)) {
        return await this.mergeData(filePath, doctype, data, options);
      }
      
      // Create new file or overwrite existing
      return await this.createNewFile(filePath, doctype, data, options);
      
    } catch (error) {
      this.logger.error(`Failed to save data to JSON: ${error}`);
      return {
        success: false,
        size: 0,
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private async createNewFile(filePath: string, doctype: string, data: unknown, options: SaveDataOptions): Promise<SaveDataResult> {
    const now = new Date().toISOString();
    
    // Prepare data structure
    const dataToSave: StoredDataStructure = {
      doctype,
      extractedAt: now,
      lastModified: now,
      metadata: {
        connectorName: options.metadata?.connectorName as string || 'unknown',
        connectorVersion: options.metadata?.connectorVersion as string || '1.0.0',
        extractedBy: options.metadata?.extractedBy as string || 'Unknown Connector',
        primaryKeyFields: Array.isArray(options.primaryKey) ? options.primaryKey : options.primaryKey ? [options.primaryKey] : undefined,
        mergeStrategy: options.mergeStrategy || 'replace',
        ...options.metadata
      },
      data: this.normalizeDataStructure(data),
      history: options.preserveHistory ? [{
        timestamp: now,
        operation: 'create',
        itemsAffected: this.countItems(data),
        summary: 'Initial data creation'
      }] : undefined
    };
    
    // Convert to JSON
    const jsonString = this.prettyPrint 
      ? JSON.stringify(dataToSave, null, 2)
      : JSON.stringify(dataToSave);
    
    await fs.writeFile(filePath, jsonString, 'utf8');
    
    const stats = await fs.stat(filePath);
    const itemCount = this.countItems(data);
    
    this.logger.info(`Data created: ${path.relative(this.baseDirectory, filePath)} (${stats.size} bytes, ${itemCount} items)`);
    
    return {
      success: true,
      operation: 'create',
      filePath,
      size: stats.size,
      timestamp: now,
      itemsProcessed: itemCount,
      itemsCreated: itemCount,
      itemsUpdated: 0,
      metadata: {
        format: 'json',
        compressed: false,
        doctype
      }
    };
  }

  private async mergeData(filePath: string, doctype: string, newData: unknown, options: SaveDataOptions): Promise<SaveDataResult> {
    const now = new Date().toISOString();
    
    // Load existing data
    const existingDataString = await fs.readFile(filePath, 'utf8');
    const existingStructure: StoredDataStructure = JSON.parse(existingDataString);
    
    // Perform merge based on strategy
    const mergeResult = await this.performMerge(existingStructure, newData, options);
    
    // Update metadata
    existingStructure.lastModified = now;
    existingStructure.metadata = {
      ...existingStructure.metadata,
      ...options.metadata,
      primaryKeyFields: Array.isArray(options.primaryKey) ? options.primaryKey : options.primaryKey ? [options.primaryKey] : existingStructure.metadata.primaryKeyFields,
      mergeStrategy: options.mergeStrategy || existingStructure.metadata.mergeStrategy || 'replace'
    };
    
    // Update data
    existingStructure.data = mergeResult.mergedData as { items?: unknown[]; [key: string]: unknown };
    
    // Add history entry if enabled
    if (options.preserveHistory !== false) {
      if (!existingStructure.history) existingStructure.history = [];
      existingStructure.history.push({
        timestamp: now,
        operation: 'merge',
        itemsAffected: mergeResult.details.newItems + mergeResult.details.updatedItems,
        summary: `Merged ${mergeResult.details.newItems} new, ${mergeResult.details.updatedItems} updated items`
      });
    }
    
    // Save merged data
    const jsonString = this.prettyPrint 
      ? JSON.stringify(existingStructure, null, 2)
      : JSON.stringify(existingStructure);
    
    await fs.writeFile(filePath, jsonString, 'utf8');
    
    const stats = await fs.stat(filePath);
    
    this.logger.info(`Data merged: ${path.relative(this.baseDirectory, filePath)} (${stats.size} bytes)`);
    this.logger.info(`Merge result: ${mergeResult.details.newItems} new, ${mergeResult.details.updatedItems} updated, ${mergeResult.details.unchangedItems} unchanged`);
    
    return {
      success: true,
      operation: 'merge',
      filePath,
      size: stats.size,
      timestamp: now,
      itemsProcessed: mergeResult.details.totalItems,
      itemsCreated: mergeResult.details.newItems,
      itemsUpdated: mergeResult.details.updatedItems,
      mergeDetails: mergeResult.details,
      metadata: {
        format: 'json',
        compressed: false,
        doctype
      }
    };
  }

  private async performMerge(existingStructure: StoredDataStructure, newData: unknown, options: SaveDataOptions): Promise<{
    mergedData: unknown;
    details: MergeOperationDetails;
  }> {
    const primaryKeyFields = Array.isArray(options.primaryKey) ? options.primaryKey : [options.primaryKey!];
    const mergeStrategy = options.mergeStrategy || 'replace';
    
    const normalizedNewData = this.normalizeDataStructure(newData);
    const existingData = existingStructure.data;
    
    // Handle array-based data (like YouTube videos)
    if (normalizedNewData.items && Array.isArray(normalizedNewData.items)) {
      return this.mergeArrayData(existingData, normalizedNewData as { items: unknown[] }, primaryKeyFields, mergeStrategy, options);
    }
    
    // Handle object-based data
    return this.mergeObjectData(existingData, normalizedNewData, mergeStrategy, options);
  }

  private mergeArrayData(existingData: unknown, newData: { items: unknown[] }, primaryKeyFields: string[], mergeStrategy: string, options: SaveDataOptions): {
    mergedData: unknown;
    details: MergeOperationDetails;
  } {
    const existingItems = (existingData as { items?: unknown[] }).items || [];
    const newItems = newData.items;
    
    const details: MergeOperationDetails = {
      totalItems: newItems.length,
      newItems: 0,
      updatedItems: 0,
      unchangedItems: 0,
      conflictItems: 0,
      lastModified: new Date().toISOString(),
      mergeStrategy,
      primaryKeyFields
    };
    
    // Create a map of existing items by primary key
    const existingMap = new Map<string, { item: unknown; index: number }>();
    existingItems.forEach((item, index) => {
      const key = this.generatePrimaryKey(item, primaryKeyFields);
      if (key) {
        existingMap.set(key, { item, index });
      }
    });

    // Nouvelle stratégie : sync-preserve-nonnull
    if (mergeStrategy === 'sync-preserve-nonnull') {
      const mergedItems: unknown[] = [];
      for (const newItem of newItems) {
        const key = this.generatePrimaryKey(newItem, primaryKeyFields);
        if (!key) {
          // No primary key, add as new item
          mergedItems.push(newItem);
          details.newItems++;
          continue;
        }
        const existing = existingMap.get(key);
        if (!existing) {
          // New item
          mergedItems.push(newItem);
          details.newItems++;
        } else {
          // Merge champ par champ en préservant les anciennes valeurs non-nulles
          const mergedItem = this.mergeItemsPreserveNonNull(existing.item, newItem);
          mergedItems.push(mergedItem);
          if (JSON.stringify(existing.item) !== JSON.stringify(mergedItem)) {
            details.updatedItems++;
          } else {
            details.unchangedItems++;
          }
        }
      }
      // Les items absents de la nouvelle extraction sont supprimés (pas ajoutés)
      return {
        mergedData: {
          ...newData,
          items: mergedItems
        },
        details
      };
    }

    // Correction : stratégie 'replace' = écrasement total
    if (mergeStrategy === 'replace') {
      details.newItems = newItems.length;
      details.updatedItems = 0;
      details.unchangedItems = 0;
      return {
        mergedData: {
          ...newData,
          items: newItems
        },
        details
      };
    }

    // Correction : stratégie 'append' = concaténation simple
    if (mergeStrategy === 'append') {
      const mergedItems = [...existingItems, ...newItems];
      details.newItems = newItems.length;
      details.updatedItems = 0;
      details.unchangedItems = existingItems.length;
      return {
        mergedData: {
          ...newData,
          items: mergedItems
        },
        details
      };
    }
    
    const mergedItems = [...existingItems];
    
    // Process new items
    for (const newItem of newItems) {
      const key = this.generatePrimaryKey(newItem, primaryKeyFields);
      if (!key) {
        // No primary key, add as new item
        mergedItems.push(newItem);
        details.newItems++;
        continue;
      }
      
      const existing = existingMap.get(key);
      if (!existing) {
        // New item
        mergedItems.push(newItem);
        details.newItems++;
      } else {
        // Update existing item
        const mergedItem = this.mergeItems(existing.item, newItem, mergeStrategy, options);
        mergedItems[existing.index] = mergedItem;
        
        if (JSON.stringify(existing.item) !== JSON.stringify(mergedItem)) {
          details.updatedItems++;
        } else {
          details.unchangedItems++;
        }
      }
    }
    
    return {
      mergedData: {
        ...newData,
        items: mergedItems
      },
      details
    };
  }

  private mergeObjectData(existingData: unknown, newData: unknown, mergeStrategy: string, options: SaveDataOptions): {
    mergedData: unknown;
    details: MergeOperationDetails;
  } {
    const details: MergeOperationDetails = {
      totalItems: 1,
      newItems: 0,
      updatedItems: 1,
      unchangedItems: 0,
      conflictItems: 0,
      lastModified: new Date().toISOString(),
      mergeStrategy,
      primaryKeyFields: []
    };
    
    const mergedData = this.mergeItems(existingData, newData, mergeStrategy, options);
    
    return { mergedData, details };
  }

  private mergeItems(existing: unknown, incoming: unknown, strategy: string, options: SaveDataOptions): unknown {
    if (options.customMerger) {
      return options.customMerger(existing, incoming);
    }
    
    switch (strategy) {
      case 'replace':
        return incoming;
      case 'merge':
        return this.deepMerge(existing, incoming);
      case 'append':
        if (Array.isArray(existing) && Array.isArray(incoming)) {
          return [...existing, ...incoming];
        }
        return incoming;
      default:
        return incoming;
    }
  }

  private deepMerge(target: unknown, source: unknown): unknown {
    if (source === null || source === undefined) return target;
    if (target === null || target === undefined) return source;
    
    if (typeof target !== 'object' || typeof source !== 'object') {
      return source;
    }
    
    if (Array.isArray(source)) {
      return source;
    }
    
    const result = { ...target as Record<string, unknown> };
    const sourceObj = source as Record<string, unknown>;
    
    for (const key in sourceObj) {
      if (key in sourceObj) {
        if (typeof result[key] === 'object' && typeof sourceObj[key] === 'object' && 
            !Array.isArray(result[key]) && !Array.isArray(sourceObj[key])) {
          result[key] = this.deepMerge(result[key], sourceObj[key]);
        } else {
          result[key] = sourceObj[key];
        }
      }
    }
    
    return result;
  }

  private generatePrimaryKey(item: unknown, keyFields: string[]): string | null {
    if (!item || typeof item !== 'object') return null;
    
    const obj = item as Record<string, unknown>;
    const keyParts: string[] = [];
    
    for (const field of keyFields) {
      const value = obj[field];
      if (value === undefined || value === null) return null;
      keyParts.push(String(value));
    }
    
    return keyParts.join('|');
  }

  private normalizeDataStructure(data: unknown): { items?: unknown[]; [key: string]: unknown } {
    // If data is an array, wrap it in an items property
    if (Array.isArray(data)) {
      return { items: data };
    }
    
    // If data is an object with an items array, use as-is
    if (data && typeof data === 'object' && 'items' in data) {
      return data as { items?: unknown[]; [key: string]: unknown };
    }
    
    // For other data structures, return as-is
    return data as { [key: string]: unknown };
  }

  private countItems(data: unknown): number {
    if (Array.isArray(data)) {
      return data.length;
    }
    
    if (data && typeof data === 'object' && 'items' in data && Array.isArray((data as { items: unknown[] }).items)) {
      return (data as { items: unknown[] }).items.length;
    }
    
    return 1;
  }

  async cleanup(): Promise<void> {
    // JSON storage doesn't need explicit cleanup
    this.logger.debug('JSON storage cleanup completed');
  }

  // Ajoute une méthode utilitaire pour le merge champ par champ en préservant les valeurs non-nulles
  private mergeItemsPreserveNonNull(existing: unknown, incoming: unknown): unknown {
    if (typeof existing !== 'object' || existing === null || typeof incoming !== 'object' || incoming === null) {
      return incoming ?? existing;
    }
    const result: Record<string, unknown> = { ...existing as Record<string, unknown> };
    for (const key of Object.keys(incoming as Record<string, unknown>)) {
      const newValue = (incoming as Record<string, unknown>)[key];
      if (newValue === null || newValue === undefined) {
        // Preserve old value
        continue;
      } else {
        result[key] = newValue;
      }
    }
    return result;
  }
}

// Database storage provider (placeholder for future implementation)
class DatabaseStorageProvider implements IStorageProvider {
  private config: DataStorageConfiguration;

  constructor(config: DataStorageConfiguration, logger: Logger) {
    this.config = config;
    void logger; // Suppress unused parameter warning
  }

  async initialize(): Promise<void> {
    throw new Error(`Database storage (${this.config.type}) is not yet implemented. Please use 'json' storage type for now.`);
  }

  async saveData(doctype: string, data: unknown, options: SaveDataOptions): Promise<SaveDataResult> {
    // Suppress unused parameter warnings for future implementation
    void doctype;
    void data;
    void options;
    
    return {
      success: false,
      size: 0,
      timestamp: new Date().toISOString(),
      error: `Database storage (${this.config.type}) is not yet implemented`
    };
  }

  async cleanup(): Promise<void> {
    // No cleanup needed for unimplemented storage
  }
}

// Main DataStorageManager class
export class DataStorageManager {
  private provider: IStorageProvider;
  private config: DataStorageConfiguration;
  private logger: Logger;

  constructor(config: DataStorageConfiguration, logger: Logger) {
    this.config = config;
    this.logger = logger;
    
    // Initialize the appropriate storage provider
    switch (config.type) {
      case 'json':
        this.provider = new JsonStorageProvider(config, logger);
        break;
      case 'sqlite':
      case 'postgresql':
      case 'mysql':
        this.provider = new DatabaseStorageProvider(config, logger);
        break;
      default:
        throw new Error(`Unsupported storage type: ${config.type}`);
    }
  }

  async initialize(): Promise<void> {
    await this.provider.initialize();
    this.logger.info(`Data storage manager initialized with type: ${this.config.type}`);
  }

  async saveData(doctype: string, data: unknown, options: SaveDataOptions = {}): Promise<SaveDataResult> {
    // Validate inputs
    if (!doctype || typeof doctype !== 'string') {
      throw new Error('doctype must be a non-empty string');
    }
    
    if (data === undefined || data === null) {
      throw new Error('data cannot be null or undefined');
    }
    
    this.logger.debug(`Saving data for doctype: ${doctype}`);
    
    const result = await this.provider.saveData(doctype, data, options);
    
    if (result.success) {
      this.logger.info(`Successfully saved data for doctype: ${doctype}`);
    } else {
      this.logger.error(`Failed to save data for doctype: ${doctype} - ${result.error}`);
    }
    
    return result;
  }

  async cleanup(): Promise<void> {
    await this.provider.cleanup();
    this.logger.debug('Data storage manager cleanup completed');
  }

  // Utility method to get storage statistics
  async getStorageStats(): Promise<{ type: string; totalFiles?: number; totalSize?: number }> {
    if (this.config.type === 'json') {
      try {
        const baseDir = this.config.options.baseDirectory || './tomodata-data/downloads';
        if (await fs.pathExists(baseDir)) {
          const files = await this.getAllJsonFiles(baseDir);
          let totalSize = 0;
          
          for (const file of files) {
            const stats = await fs.stat(file);
            totalSize += stats.size;
          }
          
          return {
            type: this.config.type,
            totalFiles: files.length,
            totalSize
          };
        }
      } catch (error) {
        this.logger.debug(`Error getting storage stats: ${error}`);
      }
    }
    
    return { type: this.config.type };
  }

  private async getAllJsonFiles(dir: string): Promise<string[]> {
    const files: string[] = [];
    const entries = await fs.readdir(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      if (entry.isDirectory()) {
        files.push(...await this.getAllJsonFiles(fullPath));
      } else if (entry.isFile() && entry.name.endsWith('.json')) {
        files.push(fullPath);
      }
    }
    
    return files;
  }
} 