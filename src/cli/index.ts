import minimist from 'minimist';
import prompts from 'prompts';
import { <PERSON>odataCore } from '../core/TomodataCore';
import { createLogger } from '../utils/logger';
import { sortVideosCommand, parseWeights, findYouTubeDataFiles } from './sort-videos';
import type { ScoringWeights } from '../video-sorter/VideoSorter';
import * as fs from 'fs';

// Type definitions for command options
interface RunOptions {
  debug?: boolean;
  path?: string;
  profile?: boolean;
}

interface SortVideoOptions {
  top?: string;
  export?: 'json' | 'csv';
  weights?: string;
  favorites?: string[];
  explain?: boolean;
  statistics?: boolean;
  interactive?: boolean;
  'find-files'?: boolean;
}

interface CleanupOptions {
  days?: string;
}

// Create logger
const logger = createLogger();

// Create core instance
const core = new TomodataCore(logger);

// Parse command line arguments
const argv = minimist(process.argv.slice(2));

// Extract command and remaining args
const command = argv._[0];
const subCommand = argv._[1];

// Helper function to show help
function showHelp() {
  console.log(`
tomodata v0.1.0 - Automated personal data extraction tool

Usage:
  tomodata <command> [options]

Commands:
  run <service>                    Run data extraction for a specific service
    -d, --debug                    Run in debug mode (visible browser)
    -p, --path <path>              Download path
    --no-profile                   Use temporary session instead of persistent browser profile

  list                             List available services

  info <service>                   Show detailed information about a service

  clear-session <service>          Clear saved session for a service

  clear-profile <service>          Clear saved browser profile for a service

  list-profiles                    List all browser profiles

  cleanup-profiles                 Clean up old unused browser profiles
    -d, --days <days>              Maximum age in days (default: 30)

  sort-videos [file]               Sort and analyze YouTube watch later videos by priority
    -t, --top <number>             Number of top recommendations to show (default: 10)
    -e, --export <format>          Export sorted playlist (json or csv)
    -w, --weights <json>           Custom scoring weights as JSON string
    -f, --favorites <videoIds...>  Mark specific video IDs as favorites
    --explain                      Show scoring explanation for each video
    --statistics                   Show detailed statistics about the video collection
    --interactive                  Interactive mode for marking favorites
    --find-files                   List available YouTube data files
                                   (Requires new storage format - run 'tomodata run youtube' first)

  help                             Show this help message

Examples:
  tomodata run youtube --debug
  tomodata list
  tomodata sort-videos --top 20 --statistics
`);
}

// Main command handler
async function handleCommand() {
  try {
    switch (command) {
      case 'run':
        await handleRunCommand();
        break;
      case 'list':
        await handleListCommand();
        break;
      case 'info':
        await handleInfoCommand();
        break;
      case 'clear-session':
        await handleClearSessionCommand();
        break;
      case 'clear-profile':
        await handleClearProfileCommand();
        break;
      case 'list-profiles':
        await handleListProfilesCommand();
        break;
      case 'cleanup-profiles':
        await handleCleanupProfilesCommand();
        break;
      case 'sort-videos':
        await handleSortVideosCommand();
        break;
      case 'help':
      case '--help':
      case '-h':
        showHelp();
        break;
      default:
        if (!command) {
          showHelp();
        } else {
          console.error(`Unknown command: ${command}`);
          console.log('Use "tomodata help" for usage information.');
          process.exit(1);
        }
    }
  } catch (error) {
    logger.error(`Command failed: ${error}`);
    process.exit(1);
  }
}

async function handleRunCommand() {
  const service = subCommand;
  if (!service) {
    console.error('Service name is required');
    console.log('Usage: tomodata run <service> [options]');
    process.exit(1);
  }

  // Ask for master password
  const response = await prompts({
    type: 'password',
    name: 'password',
    message: 'Enter master password:',
  });

  if (!response.password) {
    console.error('Master password is required to run a service.');
    process.exit(1);
  }
  
  core.setMasterPassword(response.password);

  const options = argv as RunOptions;
  const runOptions = {
    debug: options.debug || false,
    headless: false,
    downloadPath: options.path,
    useProfile: options.profile !== false // Default to true unless --no-profile is used
  };

  logger.info(`Running Tomodata for service: ${service}`);
  await core.run(service, runOptions);
  logger.info('Execution completed successfully');
}

async function handleListCommand() {
  const services = await core.listServices();

  console.log('\nAvailable services:');
  services.forEach(service => {
    const status = service.enabled ? '✓' : '○';
    console.log(`  ${status} ${service.name} - ${service.displayName}`);
  });
  console.log('');
}

async function handleInfoCommand() {
  const service = subCommand;
  if (!service) {
    console.error('Service name is required');
    console.log('Usage: tomodata info <service>');
    process.exit(1);
  }

  const info = await core.getServiceInfo(service);

  console.log(`\nService: ${info.displayName} (${info.name})`);
  console.log(`URL: ${info.baseUrl}`);
  console.log(`Enabled: ${info.enabled ? 'Yes' : 'No'}`);
  console.log(`Last run: ${info.lastRun ? new Date(info.lastRun).toLocaleString() : 'Never'}`);
  console.log(`Valid session: ${info.hasValidSession ? 'Yes' : 'No'}`);
  console.log('');
}

async function handleClearSessionCommand() {
  const service = subCommand;
  if (!service) {
    console.error('Service name is required');
    console.log('Usage: tomodata clear-session <service>');
    process.exit(1);
  }

  await core.clearSession(service);
  console.log(`Session cleared for ${service}`);
}

async function handleClearProfileCommand() {
  const service = subCommand;
  if (!service) {
    console.error('Service name is required');
    console.log('Usage: tomodata clear-profile <service>');
    process.exit(1);
  }

  await core.clearProfile(service);
  console.log(`Profile cleared for ${service}`);
}

async function handleListProfilesCommand() {
  const profiles = await core.listProfiles();

  if (profiles.length === 0) {
    console.log('\nNo browser profiles found.');
    return;
  }

  console.log('\nBrowser profiles:');
  profiles.forEach(profile => {
    console.log(`  📂 ${profile.name}`);
    console.log(`     Created: ${profile.createdAt.toLocaleString()}`);
    console.log(`     Last used: ${profile.lastUsed.toLocaleString()}`);
    console.log(`     Size: ${profile.size}`);
    console.log('');
  });

  const stats = await core.getProfileStats();
  console.log(`Total: ${stats.totalProfiles} profiles, ${stats.activeProfiles} active, ${stats.totalSize} total size`);
  console.log('');
}

async function handleCleanupProfilesCommand() {
  const options = argv as CleanupOptions;
  const maxAgeInDays = parseInt(options.days || '30');
  await core.cleanupOldProfiles(maxAgeInDays);
  console.log(`Old profiles cleaned up (older than ${maxAgeInDays} days)`);
}

async function handleSortVideosCommand() {
  const options = argv as SortVideoOptions;

  // If --find-files is specified, just list available files
  if (options['find-files']) {
    const files = findYouTubeDataFiles();
    if (files.length === 0) {
      console.log('No YouTube data files found in the storage directory.');
      console.log('Run "tomodata run youtube" first to extract your Watch Later playlist.');
      console.log('Note: Only the new storage format is supported.');
      return;
    }

        console.log('\n📁 Available YouTube data files:');
    files.forEach((filePath, index) => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const data = JSON.parse(content);
        const itemCount = data.data?.items?.length || 0;
        const lastModified = data.lastModified || 'Unknown';
        
        console.log(`  ${index + 1}. ${filePath}`);
        console.log(`     Videos: ${itemCount} | Last modified: ${lastModified}`);
      } catch {
        console.log(`  ${index + 1}. ${filePath}`);
        console.log(`     Status: Invalid or corrupted file`);
      }
    });
    console.log('\nUse: tomodata sort-videos <file-path>');
    return;
  }

  // Get file path from subcommand or auto-find
  let file = subCommand;
  if (!file) {
    const files = findYouTubeDataFiles();
    if (files.length === 0) {
      console.log('No YouTube data files found. Run "tomodata run youtube" first.');
      console.log('Note: Only the new storage format is supported.');
      return;
    }

    file = files[0]; // Most recent file
    console.log(`Using most recent YouTube data file: ${file}`);
  }

  // Parse options
  const sortOptions: {
    top: number;
    export?: 'json' | 'csv';
    favorites?: string[];
    explain?: boolean;
    statistics?: boolean;
    interactive?: boolean;
    weights?: Partial<ScoringWeights>;
  } = {
    top: parseInt(options.top || '10'),
    export: options.export as 'json' | 'csv' | undefined,
    favorites: options.favorites,
    explain: options.explain,
    statistics: options.statistics,
    interactive: options.interactive
  };

  // Parse custom weights if provided
  if (options.weights) {
    try {
      sortOptions.weights = parseWeights(options.weights);
      console.log('Using custom scoring weights:', sortOptions.weights);
    } catch (error) {
      logger.error(`Invalid weights format: ${error}`);
      process.exit(1);
    }
  }

  await sortVideosCommand(file, sortOptions);
}

// Run the CLI
handleCommand().catch(error => {
  logger.error(`Unexpected error: ${error}`);
  process.exit(1);
});
