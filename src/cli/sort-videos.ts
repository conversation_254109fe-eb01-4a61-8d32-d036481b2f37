// Command to sort and analyze YouTube watch later videos
import { <PERSON>Sorter, VideoD<PERSON>, ScoringWeights } from '../video-sorter/VideoSorter';
import { createLogger } from '../utils/logger';
import * as fs from 'fs';
import * as path from 'path';

interface SortOptions {
  top?: number;
  export?: 'json' | 'csv';
  weights?: Partial<ScoringWeights>;
  favorites?: string[];
  explain?: boolean;
  statistics?: boolean;
  interactive?: boolean;
}

export async function sortVideosCommand(
  inputFile: string,
  options: SortOptions = {}
): Promise<void> {
  const logger = createLogger('video-sorter');

  try {
    // Validate input file
    if (!fs.existsSync(inputFile)) {
      throw new Error(`File not found: ${inputFile}`);
    }

    logger.info(`Loading video data from: ${inputFile}`);

    // Load video data
    const fileContent = fs.readFileSync(inputFile, 'utf8');
    const videoData = JSON.parse(fileContent);
    
    // Only support new storage format
    if (!videoData.doctype || !videoData.data) {
      throw new Error('Unsupported data format. This tool only supports the new storage format. Please run "tomodata run youtube" to generate a compatible file.');
    }
    
    if (!videoData.data.items || !Array.isArray(videoData.data.items)) {
      throw new Error('No videos found in the data. Expected data.items array.');
    }
    
    let videos: VideoData[] = videoData.data.items;
    logger.info(`Loaded ${videos.length} videos from ${videoData.doctype} storage`);

    if (videos.length === 0) {
      throw new Error('No videos found in the input file');
    }

    logger.info(`Loaded ${videos.length} videos`);

    // Mark favorites if provided
    if (options.favorites && options.favorites.length > 0) {
      const sorter = new VideoSorter(logger);
      options.favorites.forEach(videoId => {
        videos = sorter.markAsFavorite(videos, videoId);
      });
      logger.info(`Marked ${options.favorites.length} videos as favorites`);
    }

    // Create sorter with custom weights if provided
    const sorter = new VideoSorter(logger, options.weights);

    // Show current weights
    const weights = sorter.getWeights();
    logger.info('Using scoring weights:', weights);

    // Get statistics if requested
    if (options.statistics) {
      const stats = sorter.getStatistics(videos);
      displayStatistics(stats);
    }

    // Categorize and sort videos
    const categorized = sorter.categorizeVideos(videos);

    // Display results
    console.log('\n' + '='.repeat(50));
    console.log('🎬 ANALYSE DE VOTRE WATCH LATER');
    console.log('='.repeat(50));
    console.log(`📊 Total: ${videos.length} vidéos`);
    console.log(`⚡ Vidéos courtes (<10min): ${categorized.quickWatch.length}`);
    console.log(`🆕 Vidéos récentes (<1 jour): ${categorized.recentVideos.length}`);
    console.log(`📅 Vidéos anciennes (publiées >1 mois): ${categorized.oldVideos.length}`);
    console.log(`⭐ Favorites: ${categorized.favorites.length}`);
    console.log(`👀 Vues élevées: ${categorized.highViews.length}`);

    // Show top recommendations
    const topCount = options.top || 10;
    const topVideos = sorter.getTopPriority(videos, topCount);

    console.log('\n' + '='.repeat(50));
    console.log(`🏆 TOP ${topCount} RECOMMANDATIONS`);
    console.log('='.repeat(50));

    topVideos.forEach((video, index) => {
      const score = sorter.calculateScore(video);
      const duration = Math.round(video.duration / (1000 * 60));
      const favorite = video.isFavorite ? '⭐' : '  ';
      const publishedDate = formatRelativeDate(video.publishedDate);

      console.log(`\n${(index + 1).toString().padStart(2, ' ')}. ${favorite} [${score.toFixed(1)}pts] ${video.title}`);
      console.log(`    📺 ${video.channel}`);
      console.log(`    ⏱️  ${duration}min • 👀 ${video.views.toLocaleString()} vues${publishedDate ? ' • 📅 ' + publishedDate : ''}`);
      console.log(`    🔗 ${video.url}`);

      // Show score explanation if requested
      if (options.explain) {
        const explanation = sorter.explainScore(video);
        console.log(`    📝 ${explanation.split('\n').slice(1).join('\n    ')}`);
      }
    });

    // Interactive mode for marking favorites
    if (options.interactive) {
      await interactiveMode(sorter, videos, inputFile);
    }

    // Export if requested
    if (options.export) {
      const exportData = sorter.exportSortedPlaylist(videos, options.export);
      const exportFile = path.join(
        path.dirname(inputFile),
        `sorted-videos-${new Date().toISOString().slice(0, 10)}.${options.export}`
      );
      fs.writeFileSync(exportFile, exportData, 'utf8');
      console.log(`\n📄 Exported sorted playlist to: ${exportFile}`);
    }

    // Show recommendations summary
    console.log('\n' + '='.repeat(50));
    console.log('💡 RECOMMANDATIONS');
    console.log('='.repeat(50));

    if (categorized.quickWatch.length > 0) {
      console.log(`⚡ Commencez par ${Math.min(5, categorized.quickWatch.length)} vidéos courtes pour un visionnage rapide`);
    }

    if (categorized.recentVideos.length > 0) {
      console.log(`🆕 ${categorized.recentVideos.length} vidéos récentes (<1 jour) - parfait pour rester à jour avec l'actualité`);
    }

    if (categorized.oldVideos.length > 0) {
      console.log(`📅 ${categorized.oldVideos.length} vidéos anciennes (publiées il y a +1 mois) attendent - parfait pour découvrir du contenu oublié`);
    }

    if (categorized.favorites.length === 0) {
      console.log(`⭐ Astuce: Marquez vos vidéos préférées avec --favorites pour les prioriser`);
    }

    console.log(`\n🎯 Utilisez --top ${Math.min(20, videos.length)} pour voir plus de recommandations`);
    console.log(`📊 Utilisez --statistics pour des analyses détaillées`);
    console.log(`💾 Utilisez --export json pour sauvegarder le tri`);

  } catch (error) {
    logger.error('Error sorting videos:', error);
    throw error;
  }
}

function displayStatistics(stats: ReturnType<typeof VideoSorter.prototype.getStatistics>): void {
  console.log('\n' + '='.repeat(50));
  console.log('📊 STATISTIQUES DÉTAILLÉES');
  console.log('='.repeat(50));
  console.log(`📈 Total des vidéos: ${stats.total}`);
  console.log(`⏱️  Durée totale: ${stats.totalDuration}`);
  console.log(`📊 Durée moyenne: ${stats.averageDuration}`);
  console.log(`⚡ Vidéos courtes (<10min): ${stats.shortVideos} (${((stats.shortVideos / stats.total) * 100).toFixed(1)}%)`);
  console.log(`🆕 Vidéos récentes (<1 jour): ${stats.recentVideos} (${((stats.recentVideos / stats.total) * 100).toFixed(1)}%)`);
  console.log(`📅 Vidéos anciennes (publiées >1 mois): ${stats.oldVideos} (${((stats.oldVideos / stats.total) * 100).toFixed(1)}%)`);
  console.log(`⭐ Favorites: ${stats.favorites} (${((stats.favorites / stats.total) * 100).toFixed(1)}%)`);
  console.log(`👀 Vues moyennes: ${stats.averageViews.toLocaleString()}`);

  if (stats.mostViewedVideo) {
    console.log(`🔥 Plus populaire: "${stats.mostViewedVideo.title}" (${stats.mostViewedVideo.views.toLocaleString()} vues)`);
  }

  if (stats.shortestVideo && stats.longestVideo) {
    const shortDuration = Math.round(stats.shortestVideo.duration / (1000 * 60));
    const longDuration = Math.round(stats.longestVideo.duration / (1000 * 60));
    console.log(`⏱️  Plus courte: ${shortDuration}min • Plus longue: ${longDuration}min`);
  }
}

async function interactiveMode(sorter: VideoSorter, videos: VideoData[], inputFile: string): Promise<void> {
  console.log('\n' + '='.repeat(50));
  console.log('🎮 MODE INTERACTIF');
  console.log('='.repeat(50));
  console.log('Tapez le numéro d\'une vidéo pour la marquer comme favorite');
  console.log('Tapez "save" pour sauvegarder, "quit" pour quitter');

  // This is a simplified interactive mode - in a real implementation,
  // you might want to use a proper CLI library like inquirer
  console.log('\n💡 Mode interactif simplifié - implémentation complète recommandée avec inquirer.js');
  console.log(`📁 Source file: ${inputFile}`);

  // For now, just show how to mark favorites programmatically
  const topVideos = sorter.getTopPriority(videos, 10);
  console.log('\nExemple: pour marquer la vidéo #1 comme favorite:');
  console.log(`--favorites "${topVideos[0]?.videoId}"`);
}

// Helper function to convert ISO date to relative format for display
function formatRelativeDate(dateString?: string): string {
  if (!dateString) {
    return '';
  }

  // If it's already in relative format, return as is
  if (dateString.includes('il y a') || dateString.includes('ago')) {
    return dateString;
  }

  // Try to parse as ISO date
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();

    // Convert to different time units
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffWeeks = Math.floor(diffMs / (1000 * 60 * 60 * 24 * 7));
    const diffMonths = Math.floor(diffMs / (1000 * 60 * 60 * 24 * 30));
    const diffYears = Math.floor(diffMs / (1000 * 60 * 60 * 24 * 365));

    if (diffMinutes < 60) {
      return `il y a ${diffMinutes}min`;
    } else if (diffHours < 24) {
      return `il y a ${diffHours}h`;
    } else if (diffDays < 7) {
      return `il y a ${diffDays}j`;
    } else if (diffWeeks < 4) {
      return `il y a ${diffWeeks} sem`;
    } else if (diffMonths < 12) {
      return `il y a ${diffMonths} mois`;
    } else {
      return `il y a ${diffYears} an${diffYears > 1 ? 's' : ''}`;
    }
  } catch {
    // If parsing fails, return the original string
    return dateString;
  }
}

// Helper function to parse custom weights from CLI arguments
export function parseWeights(weightsString: string): Partial<ScoringWeights> {
  try {
    return JSON.parse(weightsString);
  } catch {
    throw new Error('Invalid weights format. Use JSON format, e.g.: \'{"shortVideoBias": 60, "favoriteBonus": 120}\'');
  }
}

// Helper function to find YouTube data files (new storage format only)
export function findYouTubeDataFiles(directory: string = './tomodata-data'): string[] {
  const downloadsDir = path.join(directory, 'downloads', 'youtube');
  
  if (!fs.existsSync(downloadsDir)) {
    return [];
  }

  const files: string[] = [];
  const items = fs.readdirSync(downloadsDir);

  for (const item of items) {
    const fullPath = path.join(downloadsDir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isFile() && item.includes('youtube-watch-later') && item.endsWith('.json')) {
      files.push(fullPath);
    }
  }

  return files.sort((a, b) => {
    // Sort by modification time, newest first
    const aStat = fs.statSync(a);
    const bStat = fs.statSync(b);
    return bStat.mtime.getTime() - aStat.mtime.getTime();
  });
}
