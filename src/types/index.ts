import { Page } from 'patchright';
import { SecurityManager } from '../security/SecurityManager';

// Core interfaces for Tomodata system

export interface SessionData {
  cookies: Array<{
    name: string;
    value: string;
    domain: string;
    path: string;
    expires?: number;
    httpOnly?: boolean;
    secure?: boolean;
  }>;
  localStorage: Record<string, string>;
  sessionStorage: Record<string, string>;
  timestamp: Date;
  expiresAt?: Date;
}

// New interface for browser profile management
export interface ProfileData {
  connectorName: string;
  profilePath: string;
  createdAt: Date;
  lastUsed: Date;
  isActive: boolean;
  metadata?: {
    userAgent?: string;
    viewportSize?: { width: number; height: number };
    extensions?: string[];
    [key: string]: unknown;
  };
}

export interface ConnectorConfiguration {
  name: string;
  displayName: string;
  baseUrl: string;
  selectors: {
    username: string;
    password: string;
    loginButton: string;
    [key: string]: string;
  };
  downloadPatterns: string[];
  fileNaming: string;
  // Optional additional configuration that connectors can extend
  navigation?: {
    documentsUrl?: string;
    attestationsUrl?: string;
    [key: string]: string | undefined;
  };
  timeouts?: {
    navigation?: number;
    download?: number;
    authentication?: number;
    [key: string]: number | undefined;
  };
  // Allow any additional properties that specific connectors might need
  [key: string]: unknown;
}

export interface UserConfiguration {
  id: string;
  preferences: {
    downloadPath: string;
    autoRename: boolean;
    organizationRules?: OrganizationRules;
  };
}

export interface ServiceConfiguration {
  enabled: boolean;
  credentials?: string; // encrypted data
  lastRun?: Date;
  customRules?: Record<string, unknown>;
}

export interface TomodataConfiguration {
  user: UserConfiguration;
  services: Record<string, ServiceConfiguration>;
  global: GlobalConfiguration;
}

export interface GlobalConfiguration {
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  maxRetries: number;
  timeout: number;
  dataStorage: DataStorageConfiguration;
}

// Data storage configuration
export interface DataStorageConfiguration {
  type: 'json' | 'sqlite' | 'postgresql' | 'mysql';
  options: {
    // For JSON storage
    baseDirectory?: string;
    prettyPrint?: boolean;
    compression?: boolean;
    
    // For database storage
    host?: string;
    port?: number;
    database?: string;
    username?: string;
    password?: string;
    ssl?: boolean;
    
    // Common options
    maxFileSize?: number;
    backupEnabled?: boolean;
    retentionDays?: number;
  };
}

export interface OrganizationRules {
  folderPattern: string;
  subfoldersEnabled: boolean;
  yearSeparation: boolean;
}

export interface DownloadOptions {
  targetPath: string;
  namingPattern: string;
  overwriteExisting: boolean;
  validateIntegrity: boolean;
}

export interface FileMetadata {
  originalName: string;
  downloadDate: Date;
  service: string;
  size: number;
  type: string;
}

export interface RunOptions {
  debug?: boolean;
  headless?: boolean;
  downloadPath?: string;
  useProfile?: boolean; // Use browser profiles instead of sessions
}

export interface DataExtractionResult {
  success: boolean;
  downloads: Array<{
    filename: string;
    path: string;
    size: number;
  }>;
  errors: string[];
  metadata: Record<string, unknown>;
}

export interface ConnectorContext {
  downloadPath: string;
  sessionData?: SessionData;
  profileData?: ProfileData;
  useProfile: boolean;
  debug: boolean;
  headless: boolean;
  page?: Page; // Page can be passed for advanced scenarios
  securityManager: SecurityManager;
  masterPassword?: string;
}

// Data saving interfaces
export interface SaveDataOptions {
  filename?: string;
  tableName?: string; // for database storage
  metadata?: Record<string, unknown>;
  overwrite?: boolean;
  compress?: boolean;
  format?: 'json' | 'csv' | 'xml';
  schema?: Record<string, string>; // for database: field name -> type mapping
  
  // Data merging options
  primaryKey?: string | string[]; // Field(s) to use as primary key for merging
  mergeStrategy?: 'replace' | 'merge' | 'append' | 'custom' | 'sync-preserve-nonnull'; // How to handle existing data
  customMerger?: (existing: unknown, incoming: unknown) => unknown; // Custom merge function
  updateTimestamp?: boolean; // Whether to update lastModified timestamp on merge
  preserveHistory?: boolean; // Keep history of changes
}

export interface SaveDataResult {
  success: boolean;
  filePath?: string;
  tableId?: string;
  recordId?: string | number;
  size: number;
  timestamp: string;
  metadata?: Record<string, unknown>;
  error?: string;
  
  // Merge operation results
  operation?: 'create' | 'update' | 'merge'; // Type of operation performed
  itemsProcessed?: number; // Number of items processed
  itemsCreated?: number; // New items created
  itemsUpdated?: number; // Existing items updated
  mergeDetails?: MergeOperationDetails; // Detailed merge information
}

export interface MergeOperationDetails {
  totalItems: number;
  newItems: number;
  updatedItems: number;
  unchangedItems: number;
  conflictItems: number;
  lastModified: string;
  mergeStrategy: string;
  primaryKeyFields: string[];
}

// Structure for stored data with merge capabilities
export interface StoredDataStructure {
  doctype: string;
  extractedAt: string;
  lastModified: string;
  metadata: {
    connectorName: string;
    connectorVersion: string;
    extractedBy: string;
    primaryKeyFields?: string[];
    mergeStrategy?: string;
    [key: string]: unknown;
  };
  data: {
    items?: unknown[]; // For array-based data like YouTube videos
    [key: string]: unknown; // For other data structures
  };
  history?: {
    timestamp: string;
    operation: 'create' | 'update' | 'merge';
    itemsAffected: number;
    summary: string;
  }[];
}
