import * as winston from 'winston';
import * as path from 'path';

export function createLogger(dataDir: string = './tomodata-data'): winston.Logger {
  const logDir = path.join(dataDir, 'logs');

  // Create logger with multiple transports
  const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json()
    ),
    defaultMeta: { service: 'tomodata' },
    transports: [
      // Write all logs to `tomodata.log`
      new winston.transports.File({
        filename: path.join(logDir, 'tomodata.log'),
        maxsize: 5242880, // 5MB
        maxFiles: 5,
        tailable: true
      }),
      // Write all error logs to `error.log`
      new winston.transports.File({
        filename: path.join(logDir, 'error.log'),
        level: 'error',
        maxsize: 5242880, // 5MB
        maxFiles: 3,
        tailable: true
      })
    ]
  });

  // Add console transport for development
  if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }));
  }

  return logger;
}
