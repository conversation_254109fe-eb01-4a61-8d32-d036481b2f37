#!/usr/bin/env node

// Main entry point for Tomodata
export { TomodataCore } from './core/TomodataCore';
export { SessionManager } from './session/SessionManager';
export { IConnector, Credentials } from './connectors/base/ConnectorInterface';
export { BaseConnector } from './connectors/base/BaseConnector';
export * from './types';

// Run CLI if called directly
if (require.main === module) {
  require('./cli');
}
