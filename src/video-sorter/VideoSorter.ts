// Video sorting and scoring utility
import { Logger } from 'winston';

interface VideoData {
  index: number;
  title: string;
  channel: string;
  channelId: string;
  duration: number; // Duration in milliseconds
  views: number; // Number of views
  url: string;
  thumbnail: string;
  videoId: string;
  extractedAt: string;
  isFavorite?: boolean; // New field for favorites
  publishedDate?: string; // Date when the video was published
  priorityScore?: number; // Calculated score
}

interface ScoringWeights {
  shortVideoBias: number;     // Bonus pour vidéos < 10min
  ageBias: number;            // Bonus pour vidéos > 1 mois
  recentBonus: number;        // Bonus pour vidéos publiées < 1 jour
  viewsWeight: number;        // Poids des vues (logarithmique)
  durationWeight: number;     // Poids de la durée (favorise les courtes)
  favoriteBonus: number;      // Bonus pour les favorites
}

export class VideoSorter {
  private logger: Logger;
  private weights: ScoringWeights;

  constructor(logger: Logger, weights?: Partial<ScoringWeights>) {
    this.logger = logger;

    // Default scoring weights (easily customizable)
    this.weights = {
      shortVideoBias: 50,      // +50 points for videos < 10min
      ageBias: 30,             // +30 points for videos published > 1 month ago
      recentBonus: 50,         // +50 points for videos published < 1 day ago
      viewsWeight: 0.15,       // Views contribution (logarithmic scale)
      durationWeight: 0.8,     // Duration penalty factor
      favoriteBonus: 100,      // +100 points for favorites
      ...weights
    };
  }

  /**
   * Calculate priority score for a video
   * Higher score = higher priority
   */
  calculateScore(video: VideoData, currentDate: Date = new Date()): number {
    let score = 0;

    // 1. Short video bias (<10 minutes)
    const durationMinutes = video.duration / (1000 * 60);
    if (durationMinutes < 10) {
      score += this.weights.shortVideoBias;
      this.logger.debug(`Short video bonus (+${this.weights.shortVideoBias}): ${video.title}`);
    }

    // 2. Age bias (videos published more than 1 month ago)
    // Use publishedDate if available, otherwise fall back to extractedAt
    const videoDate = video.publishedDate ? new Date(video.publishedDate) : new Date(video.extractedAt);
    const daysSincePublished = (currentDate.getTime() - videoDate.getTime()) / (1000 * 60 * 60 * 24);

    if (daysSincePublished > 30) {
      // Linear bonus up to 60 days, then plateaus
      const ageBonus = Math.min(this.weights.ageBias, (daysSincePublished - 30) * 2);
      score += ageBonus;
      this.logger.debug(`Age bonus (+${ageBonus.toFixed(1)}): ${video.title} (${daysSincePublished.toFixed(0)} days since published)`);
    }

    // 2.5. Recent video bonus (videos published less than 1 day ago)
    if (daysSincePublished < 1) {
      score += this.weights.recentBonus;
      this.logger.debug(`Recent video bonus (+${this.weights.recentBonus}): ${video.title} (${(daysSincePublished * 24).toFixed(1)} hours since published)`);
    }

    // 3. Views contribution (logarithmic to avoid extreme values)
    if (video.views > 0) {
      const viewsScore = Math.log10(video.views) * this.weights.viewsWeight;
      score += viewsScore;
      this.logger.debug(`Views bonus (+${viewsScore.toFixed(1)}): ${video.title} (${video.views.toLocaleString()} views)`);
    }

    // 4. Duration penalty (shorter is better)
    const durationPenalty = durationMinutes * this.weights.durationWeight;
    score -= durationPenalty;
    this.logger.debug(`Duration penalty (-${durationPenalty.toFixed(1)}): ${video.title} (${durationMinutes.toFixed(1)} min)`);

    // 5. Favorite bonus
    if (video.isFavorite) {
      score += this.weights.favoriteBonus;
      this.logger.debug(`Favorite bonus (+${this.weights.favoriteBonus}): ${video.title}`);
    }

    return Math.round(score * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Sort videos by priority score (descending)
   */
  sortByPriority(videos: VideoData[]): VideoData[] {
    const videosWithScores = videos.map(video => ({
      ...video,
      priorityScore: this.calculateScore(video)
    }));

    return videosWithScores.sort((a, b) => (b.priorityScore || 0) - (a.priorityScore || 0));
  }

  /**
   * Get top N videos by priority
   */
  getTopPriority(videos: VideoData[], count: number): VideoData[] {
    return this.sortByPriority(videos).slice(0, count);
  }

  /**
   * Filter and sort videos by categories
   */
  categorizeVideos(videos: VideoData[]): {
    quickWatch: VideoData[];     // < 10 min
    oldVideos: VideoData[];      // published > 1 month ago
    recentVideos: VideoData[];   // published < 1 day ago
    favorites: VideoData[];      // Marked as favorite
    highViews: VideoData[];      // > median views
    sorted: VideoData[];         // All videos sorted by score
  } {
    const sorted = this.sortByPriority(videos);
    const medianViews = this.calculateMedianViews(videos);
    const currentDate = new Date();

    return {
      quickWatch: sorted.filter(v => v.duration < 10 * 60 * 1000),
      oldVideos: sorted.filter(v => {
        const videoDate = v.publishedDate ? new Date(v.publishedDate) : new Date(v.extractedAt);
        const daysSince = (currentDate.getTime() - videoDate.getTime()) / (1000 * 60 * 60 * 24);
        return daysSince > 30;
      }),
      recentVideos: sorted.filter(v => {
        const videoDate = v.publishedDate ? new Date(v.publishedDate) : new Date(v.extractedAt);
        const daysSince = (currentDate.getTime() - videoDate.getTime()) / (1000 * 60 * 60 * 24);
        return daysSince < 1;
      }),
      favorites: sorted.filter(v => v.isFavorite),
      highViews: sorted.filter(v => v.views > medianViews),
      sorted
    };
  }

  /**
   * Update scoring weights
   */
  updateWeights(newWeights: Partial<ScoringWeights>): void {
    this.weights = { ...this.weights, ...newWeights };
    this.logger.info('Scoring weights updated:', this.weights);
  }

  /**
   * Get current scoring weights
   */
  getWeights(): ScoringWeights {
    return { ...this.weights };
  }

  /**
   * Get scoring explanation for a video
   */
  explainScore(video: VideoData): string {
    const score = this.calculateScore(video);
    const durationMinutes = video.duration / (1000 * 60);
    const videoDate = video.publishedDate ? new Date(video.publishedDate) : new Date(video.extractedAt);
    const daysSincePublished = (Date.now() - videoDate.getTime()) / (1000 * 60 * 60 * 24);

    let explanation = `Score total: ${score.toFixed(1)} points\n`;
    explanation += `📝 Détail du calcul:\n`;

    if (durationMinutes < 10) {
      explanation += `  ⚡ Vidéo courte (<10min): +${this.weights.shortVideoBias} pts\n`;
    }

    if (daysSincePublished > 30) {
      const ageBonus = Math.min(this.weights.ageBias, (daysSincePublished - 30) * 2);
      const dateType = video.publishedDate ? 'publiée' : 'extraite';
      explanation += `  📅 Vidéo ancienne (${daysSincePublished.toFixed(0)} jours depuis ${dateType}): +${ageBonus.toFixed(1)} pts\n`;
    }

    if (daysSincePublished < 1) {
      const dateType = video.publishedDate ? 'publiée' : 'extraite';
      explanation += `  🆕 Vidéo récente (${(daysSincePublished * 24).toFixed(1)}h depuis ${dateType}): +${this.weights.recentBonus} pts\n`;
    }

    if (video.views > 0) {
      const viewsScore = Math.log10(video.views) * this.weights.viewsWeight;
      explanation += `  👀 Vues (${video.views.toLocaleString()}): +${viewsScore.toFixed(1)} pts\n`;
    }

    const durationPenalty = durationMinutes * this.weights.durationWeight;
    explanation += `  ⏱️ Pénalité durée (${durationMinutes.toFixed(1)}min): -${durationPenalty.toFixed(1)} pts\n`;

    if (video.isFavorite) {
      explanation += `  ⭐ Favorite: +${this.weights.favoriteBonus} pts\n`;
    }

    return explanation;
  }

  private calculateMedianViews(videos: VideoData[]): number {
    const sortedViews = videos.map(v => v.views).sort((a, b) => a - b);
    const mid = Math.floor(sortedViews.length / 2);

    if (sortedViews.length % 2 === 0) {
      return (sortedViews[mid - 1] + sortedViews[mid]) / 2;
    } else {
      return sortedViews[mid];
    }
  }

  /**
   * Mark video as favorite
   */
  markAsFavorite(videos: VideoData[], videoId: string): VideoData[] {
    return videos.map(video =>
      video.videoId === videoId
        ? { ...video, isFavorite: true }
        : video
    );
  }

  /**
   * Remove favorite status
   */
  unmarkAsFavorite(videos: VideoData[], videoId: string): VideoData[] {
    return videos.map(video =>
      video.videoId === videoId
        ? { ...video, isFavorite: false }
        : video
    );
  }

  /**
   * Export sorted playlist for external use
   */
  exportSortedPlaylist(videos: VideoData[], format: 'json' | 'csv' = 'json'): string {
    const sorted = this.sortByPriority(videos).map((video, index) => ({
      rank: index + 1,
      title: video.title,
      channel: video.channel,
      duration: this.formatDuration(video.duration),
      views: video.views.toLocaleString(),
      priorityScore: this.calculateScore(video),
      url: video.url,
      isFavorite: video.isFavorite || false,
      publishedDate: video.publishedDate || null,
      extractedAt: video.extractedAt
    }));

    if (format === 'csv') {
      const headers = Object.keys(sorted[0]).join(',');
      const rows = sorted.map(row =>
        Object.values(row).map(val =>
          typeof val === 'string' && val.includes(',') ? `"${val}"` : val
        ).join(',')
      ).join('\n');
      return `${headers}\n${rows}`;
    }

    return JSON.stringify(sorted, null, 2);
  }

  /**
   * Get statistics about the video collection
   */
  getStatistics(videos: VideoData[]): {
    total: number;
    totalDuration: string;
    averageDuration: string;
    shortVideos: number;
    oldVideos: number;
    recentVideos: number;
    favorites: number;
    averageViews: number;
    mostViewedVideo: VideoData | null;
    shortestVideo: VideoData | null;
    longestVideo: VideoData | null;
  } {
    const totalDurationMs = videos.reduce((sum, v) => sum + v.duration, 0);
    const averageDurationMs = totalDurationMs / videos.length;
    const shortVideos = videos.filter(v => v.duration < 10 * 60 * 1000).length;
    const currentDate = new Date();
    const oldVideos = videos.filter(v => {
      const videoDate = v.publishedDate ? new Date(v.publishedDate) : new Date(v.extractedAt);
      const daysSince = (currentDate.getTime() - videoDate.getTime()) / (1000 * 60 * 60 * 24);
      return daysSince > 30;
    }).length;
    const recentVideos = videos.filter(v => {
      const videoDate = v.publishedDate ? new Date(v.publishedDate) : new Date(v.extractedAt);
      const daysSince = (currentDate.getTime() - videoDate.getTime()) / (1000 * 60 * 60 * 24);
      return daysSince < 1;
    }).length;
    const favorites = videos.filter(v => v.isFavorite).length;
    const averageViews = videos.reduce((sum, v) => sum + v.views, 0) / videos.length;

    const sortedByViews = [...videos].sort((a, b) => b.views - a.views);
    const sortedByDuration = [...videos].sort((a, b) => a.duration - b.duration);

    return {
      total: videos.length,
      totalDuration: this.formatDuration(totalDurationMs),
      averageDuration: this.formatDuration(averageDurationMs),
      shortVideos,
      oldVideos,
      recentVideos,
      favorites,
      averageViews: Math.round(averageViews),
      mostViewedVideo: sortedByViews[0] || null,
      shortestVideo: sortedByDuration[0] || null,
      longestVideo: sortedByDuration[sortedByDuration.length - 1] || null
    };
  }

  private formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours}:${remainingMinutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}

export type { VideoData, ScoringWeights };
