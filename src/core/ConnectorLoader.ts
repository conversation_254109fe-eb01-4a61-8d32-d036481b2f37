import { Logger } from 'winston';
import { IConnector } from '../connectors/base/ConnectorInterface';
import * as fs from 'fs-extra';
import * as path from 'path';

export class ConnectorLoader {
  private logger: Logger;
  private connectorsPath: string;

  constructor(logger: Logger, connectorsPath: string = path.join(__dirname, '../connectors')) {
    this.logger = logger;
    this.connectorsPath = connectorsPath;
  }

  /**
   * Automatically discover and load all available connectors
   * @returns Map of connector name to factory function
   */
  async loadConnectors(): Promise<Map<string, () => IConnector>> {
    const connectors = new Map<string, () => IConnector>();

    try {
      // Get all directories in the connectors folder
      const connectorDirs = await this.getConnectorDirectories();

      for (const connectorDir of connectorDirs) {
        try {
          const connector = await this.loadConnector(connectorDir);
          if (connector) {
            connectors.set(connector.name, connector.factory);
            this.logger.debug(`Loaded connector: ${connector.name} (${connector.displayName})`);
          }
        } catch (error) {
          this.logger.warn(`Failed to load connector from directory ${connectorDir}: ${error}`);
        }
      }

      this.logger.info(`Loaded ${connectors.size} connectors automatically`);
    } catch (error) {
      this.logger.error(`Error loading connectors: ${error}`);
    }

    return connectors;
  }

  /**
   * Get all directories that could contain connectors (excluding base directory)
   */
  private async getConnectorDirectories(): Promise<string[]> {
    const directories: string[] = [];

    if (!await fs.pathExists(this.connectorsPath)) {
      this.logger.warn(`Connectors directory not found: ${this.connectorsPath}`);
      return directories;
    }

    const items = await fs.readdir(this.connectorsPath, { withFileTypes: true });

    for (const item of items) {
      if (item.isDirectory() && item.name !== 'base') {
        directories.push(item.name);
      }
    }

    return directories;
  }

  /**
   * Load a specific connector from its directory
   */
  private async loadConnector(connectorDir: string): Promise<{
    name: string;
    displayName: string;
    factory: () => IConnector;
  } | null> {
    const connectorPath = path.join(this.connectorsPath, connectorDir);

    // Check if the connector file exists
    const possibleFiles = [
      `${this.capitalizeFirst(connectorDir)}Connector.ts`,
      `${this.capitalizeFirst(connectorDir)}Connector.js`,
      'index.ts',
      'index.js'
    ];

    let connectorFile: string | null = null;
    for (const file of possibleFiles) {
      const filePath = path.join(connectorPath, file);
      if (await fs.pathExists(filePath)) {
        connectorFile = filePath;
        break;
      }
    }

    if (!connectorFile) {
      this.logger.debug(`No connector file found in ${connectorDir}`);
      return null;
    }

    try {
      // Dynamically import the connector
      const connectorModule = await import(connectorFile);

      // Look for the connector class - try multiple naming conventions
      const possibleExports = [
        `${this.capitalizeFirst(connectorDir)}Connector`,
        'default',
        connectorDir,
        `${connectorDir.toUpperCase()}Connector`
      ];

      let ConnectorClass: any = null;
      for (const exportName of possibleExports) {
        if (connectorModule[exportName]) {
          ConnectorClass = connectorModule[exportName];
          break;
        }
      }

      if (!ConnectorClass) {
        this.logger.debug(`No valid connector class found in ${connectorFile}`);
        return null;
      }

      // Create a temporary instance to get the connector info
      const tempInstance = new ConnectorClass(this.logger);

      // Verify it implements the connector interface
      if (!this.isValidConnector(tempInstance)) {
        this.logger.debug(`Invalid connector interface in ${connectorFile}`);
        return null;
      }

      return {
        name: tempInstance.name || connectorDir,
        displayName: tempInstance.displayName || this.capitalizeFirst(connectorDir),
        factory: () => new ConnectorClass(this.logger)
      };

    } catch (error) {
      this.logger.debug(`Error importing connector from ${connectorFile}: ${error}`);
      return null;
    }
  }

  /**
   * Validate that an object implements the IConnector interface
   */
  private isValidConnector(obj: any): obj is IConnector {
    return obj &&
      typeof obj.name === 'string' &&
      typeof obj.displayName === 'string' &&
      typeof obj.version === 'string' &&
      typeof obj.initialize === 'function' &&
      typeof obj.authenticate === 'function' &&
      typeof obj.extractData === 'function' &&
      typeof obj.cleanup === 'function' &&
      typeof obj.getConfiguration === 'function';
  }

  /**
   * Capitalize the first letter of a string
   */
  private capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}
