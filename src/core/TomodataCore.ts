import { Logger } from 'winston';
import { SessionManager } from '../session/SessionManager';
import { ProfileManager } from '../session/ProfileManager';
import { IConnector } from '../connectors/base/ConnectorInterface';
import { RunOptions, ConnectorContext, TomodataConfiguration } from '../types';
import { ConnectorLoader } from './ConnectorLoader';
import { SecurityManager } from '../security/SecurityManager';
import * as fs from 'fs-extra';
import * as path from 'path';

export class TomodataCore {
  private logger: Logger;
  private sessionManager: SessionManager;
  private profileManager: ProfileManager;
  private securityManager: SecurityManager;
  private config: TomodataConfiguration;
  private dataDir: string;
  private connectorLoader: ConnectorLoader;
  private masterPassword?: string;

  // Registry of available connectors
  private connectors: Map<string, () => IConnector> = new Map();

  constructor(logger: Logger, dataDir: string = './tomodata-data') {
    this.logger = logger;
    this.dataDir = dataDir;
    this.sessionManager = new SessionManager(path.join(dataDir, 'sessions'), logger);
    this.profileManager = new ProfileManager(dataDir, logger);
    this.securityManager = new SecurityManager(logger);
    this.connectorLoader = new ConnectorLoader(logger);

    // Initialize configuration
    this.config = this.loadConfiguration();

    // Connectors will be loaded automatically on first use via ensureConnectorsLoaded()
  }

  public setMasterPassword(password: string): void {
    this.masterPassword = password;
    this.logger.info('Master password has been set for the current session.');
  }

  // TODO: We will need to pass the master password to the connectors
  // For now, it is stored in the core instance.

  private async initializeConnectors(): Promise<void> {
    try {
      this.connectors = await this.connectorLoader.loadConnectors();
    } catch (error) {
      this.logger.error(`Failed to load connectors: ${error}`);
    }
  }

  private async ensureConnectorsLoaded(): Promise<void> {
    if (this.connectors.size === 0) {
      await this.initializeConnectors();
    }
  }



  private loadConfiguration(): TomodataConfiguration {
    const configPath = path.join(this.dataDir, 'config', 'config.json');

    try {
      if (fs.existsSync(configPath)) {
        return fs.readJsonSync(configPath);
      }
    } catch (error) {
      this.logger.warn(`Failed to load configuration: ${error}`);
    }

    // Return default configuration
    return {
      user: {
        id: 'default-user',
        preferences: {
          downloadPath: path.join(this.dataDir, 'downloads'),
          autoRename: true
        }
      },
      services: {},
      global: {
        logLevel: 'info',
        maxRetries: 3,
        timeout: 30000,
        dataStorage: {
          type: 'json',
          options: {
            baseDirectory: path.join(this.dataDir, 'storage'),
            prettyPrint: true,
            compression: false,
            backupEnabled: false,
            retentionDays: 365
          }
        }
      }
    };
  }

  private async saveConfiguration(): Promise<void> {
    const configPath = path.join(this.dataDir, 'config', 'config.json');
    await fs.ensureDir(path.dirname(configPath));
    await fs.writeJson(configPath, this.config, { spaces: 2 });
  }

  async run(serviceName: string, options: RunOptions = {}): Promise<void> {
    this.logger.info(`Starting Tomodata for service: ${serviceName}`);

    // Ensure connectors are loaded
    await this.ensureConnectorsLoaded();

    // Get connector factory
    const connectorFactory = this.connectors.get(serviceName);
    if (!connectorFactory) {
      throw new Error(`Unknown service: ${serviceName}`);
    }

    // Create connector instance
    const connector = connectorFactory();

    try {
      // Prepare download directory
      const downloadPath = options.downloadPath ||
        path.join(this.config.user.preferences.downloadPath, serviceName);
      await fs.ensureDir(downloadPath);

      // Load existing session
      const sessionData = await this.sessionManager.isSessionValid(serviceName)
        ? await this.sessionManager.loadSession(serviceName)
        : undefined;

      // Create connector context (use profiles by default, fall back to sessions if needed)
      const context: ConnectorContext = {
        downloadPath,
        sessionData: sessionData || undefined,
        useProfile: options.useProfile !== false, // Enable browser profiles by default, can be disabled
        debug: options.debug || false,
        headless: options.headless !== false, // Default to headless true, unless explicitly set to false
        securityManager: this.securityManager,
        masterPassword: this.masterPassword,
      };

      // Initialize connector
      await connector.initialize(context);

      // Attempt authentication
      const authSuccess = await connector.authenticate();
      if (!authSuccess) {
        throw new Error('Authentication failed');
      }

      // Save session after successful authentication
      const newSessionData = await connector.saveSession();
      if (newSessionData) {
        await this.sessionManager.saveSession(serviceName, newSessionData);
      }

      // Extract data
      const result = await connector.extractData();

      if (result.success) {
        this.logger.info(`Data extraction completed successfully for ${serviceName}`);
        
        if (result.downloads && result.downloads.length > 0) {
          this.logger.info(`Processing ${result.downloads.length} downloaded files...`);
          for (const download of result.downloads) {
            try {
              const sourcePath = download.path;
              const destPath = path.join(downloadPath, download.filename);
              await fs.move(sourcePath, destPath, { overwrite: true });
              this.logger.info(`Moved ${download.filename} to ${destPath}`);
            } catch (moveError) {
              this.logger.error(`Failed to move downloaded file ${download.filename}: ${moveError}`);
            }
          }
        } else {
            this.logger.info('No files to download.');
        }

        // Update service configuration
        this.config.services[serviceName] = {
          ...this.config.services[serviceName],
          enabled: true,
          lastRun: new Date()
        };
        await this.saveConfiguration();
      } else {
        this.logger.error(`Data extraction failed for ${serviceName}`);
        result.errors.forEach(error => this.logger.error(error));
      }

    } catch (error) {
      this.logger.error(`Error running service ${serviceName}: ${error}`);
      throw error;
    } finally {
      // Always cleanup
      await connector.cleanup();
    }
  }

  async listServices(): Promise<Array<{ name: string; displayName: string; enabled: boolean }>> {
    // Ensure connectors are loaded
    await this.ensureConnectorsLoaded();

    const services = [];

    for (const [name, factory] of this.connectors) {
      const connector = factory();
      const serviceConfig = this.config.services[name];

      services.push({
        name,
        displayName: connector.displayName,
        enabled: serviceConfig?.enabled || false
      });
    }

    return services;
  }

  async getServiceInfo(serviceName: string): Promise<{
    name: string;
    displayName: string;
    baseUrl: string;
    enabled: boolean;
    lastRun?: Date;
    hasValidSession: boolean;
  }> {
    // Ensure connectors are loaded
    await this.ensureConnectorsLoaded();

    const connectorFactory = this.connectors.get(serviceName);
    if (!connectorFactory) {
      throw new Error(`Unknown service: ${serviceName}`);
    }

    const connector = connectorFactory();
    const config = connector.getConfiguration();
    const serviceConfig = this.config.services[serviceName];
    const hasValidSession = await this.sessionManager.isSessionValid(serviceName);

    return {
      ...config,
      enabled: serviceConfig?.enabled || false,
      lastRun: serviceConfig?.lastRun,
      hasValidSession
    };
  }

  async clearSession(serviceName: string): Promise<void> {
    await this.sessionManager.clearSession(serviceName);
    this.logger.info(`Session cleared for ${serviceName}`);
  }

  // Profile management methods
  async clearProfile(serviceName: string): Promise<void> {
    await this.profileManager.deleteProfile(serviceName);
    this.logger.info(`Profile cleared for ${serviceName}`);
  }

  async listProfiles(): Promise<Array<{ name: string; createdAt: Date; lastUsed: Date; size: string }>> {
    const profiles = await this.profileManager.listProfiles();
    const stats = await this.profileManager.getProfileStats();

    return profiles.map(profile => ({
      name: profile.connectorName,
      createdAt: profile.createdAt,
      lastUsed: profile.lastUsed,
      size: this.formatBytes(stats.totalSize / profiles.length) // Approximate size per profile
    }));
  }

  async getProfileStats(): Promise<{
    totalProfiles: number;
    activeProfiles: number;
    totalSize: string;
  }> {
    const stats = await this.profileManager.getProfileStats();
    return {
      totalProfiles: stats.totalProfiles,
      activeProfiles: stats.activeProfiles,
      totalSize: this.formatBytes(stats.totalSize)
    };
  }

  async cleanupOldProfiles(maxAgeInDays: number = 30): Promise<void> {
    const maxAge = maxAgeInDays * 24 * 60 * 60 * 1000; // Convert days to milliseconds
    await this.profileManager.cleanupProfiles(maxAge);
    this.logger.info(`Old profiles cleaned up (older than ${maxAgeInDays} days)`);
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
