import { BaseConnector } from '../base/BaseConnector';
import { ConnectorConfiguration, DataExtractionResult } from '../../types';
import { Credentials } from '../base/ConnectorInterface';
import { Logger } from 'winston';

export class AmeliConnector extends BaseConnector {
  name = 'ameli';
  displayName = 'Assurance Maladie';
  version = '1.0.0';

  constructor(logger: Logger) {
    super(logger);
  }

  async authenticate(credentials?: Credentials): Promise<boolean> {
    if (!this.page) {
      throw new Error('Connector not initialized');
    }

    try {
      // Navigate to Ameli login page
      await this.page.goto('https://connexion.ameli.fr');

      if (credentials) {
        // Fill credentials automatically
        await this.page.fill('#username', credentials.username);
        await this.page.fill('#password', credentials.password);
        await this.page.click('#kc-login');

        // Wait for navigation after login
        await this.page.waitForLoadState('networkidle');
      } else {
        // Manual authentication - wait for user to complete
        this.logger.info(`Please complete authentication manually for ${this.displayName}`);
      }

      // Use the connector-specific isAuthenticated method to verify authentication
      const maxAttempts = 60; // Maximum 5 minutes with 5-second intervals
      let attempts = 0;

      while (attempts < maxAttempts) {
        const authenticated = await this.isAuthenticated();

        if (authenticated) {
          this.logger.info(`Authentication completed for ${this.displayName}`);
          return true;
        }

        // Wait 5 seconds before checking again
        await new Promise(resolve => setTimeout(resolve, 5000));
        attempts++;

        if (attempts % 12 === 0) { // Log every minute
          this.logger.info(`Still waiting for authentication... (${Math.floor(attempts / 12)} minute(s) elapsed)`);
        }
      }

      this.logger.error(`Authentication timeout for ${this.displayName}`);
      return false;

    } catch (error) {
      this.logger.error(`Authentication failed for ${this.displayName}: ${error}`);
      return false;
    }
  }

  async isAuthenticated(): Promise<boolean> {
    if (!this.page) {
      return false;
    }

    try {
      const currentUrl = this.page.url();
      const config = this.getConfiguration();

      // Check if we're on the login page (not authenticated)
      if (currentUrl.includes('/PortailAS/appmanager/PortailAS/assure') &&
          currentUrl.includes('login')) {
        return false;
      }

      // Check for Ameli-specific authenticated indicators
      const ameliAuthenticatedSelectors = [
        // User account information
        '.compte-assure',
        '.infos-assure',
        '#menuCompte',

        // Logout/disconnect links
        'a[href*="deconnexion"]',
        'a[href*="logout"]',

        // Main navigation menu (only visible when authenticated)
        '.nav-principale',
        '.menu-principal',

        // User dashboard elements
        '.tableau-bord',
        '.espace-personnel',

        // Documents/attestations links
        'a[href*="attestation"]',
        'a[href*="remboursements"]',

        // Account balance or recent activity
        '.solde-compte',
        '.activite-recente'
      ];

      // Check if any authentication indicator is present
      for (const selector of ameliAuthenticatedSelectors) {
        try {
          const element = await this.page.locator(selector).first();
          if (await element.isVisible({ timeout: 1000 })) {
            this.logger.debug(`Authentication confirmed: found element ${selector}`);
            return true;
          }
        } catch {
          // Continue to next selector
        }
      }

      // Additional check: try to access a protected page
      try {
        // Check if we can access the account page without being redirected
        const response = await this.page.goto(config.baseUrl + '/PortailAS/appmanager/PortailAS/assure?_nfpb=true&_pageLabel=as_info_perso_page', {
          waitUntil: 'networkidle',
          timeout: 5000
        });

        if (response && response.ok() && !this.page.url().includes('login')) {
          this.logger.debug('Authentication confirmed: successfully accessed protected page');
          return true;
        }
      } catch {
        // Failed to access protected page, likely not authenticated
      }

      return false;
    } catch (error) {
      this.logger.debug(`Error checking Ameli authentication status: ${error}`);
      return false;
    }
  }

  async extractData(): Promise<DataExtractionResult> {
    if (!this.page) {
      throw new Error('Connector not initialized');
    }

    const result: DataExtractionResult = {
      success: false,
      downloads: [],
      errors: [],
      metadata: {}
    };

    try {
      const config = this.getConfiguration();

      // Wait for the main page to load after authentication
      await this.page.waitForLoadState('networkidle');

      this.logger.info('Starting data extraction for Ameli...');

      // Use configuration values from config.json
      await this.navigateToDocuments(config);

      // Show available documents to user
      await this.displayAvailableDocuments(config);

      // For MVP: manual download assistance
      this.logger.info('Please manually download the documents you need.');
      this.logger.info('Session will be saved for future use.');

      result.success = true;
      result.metadata = {
        sessionSaved: true,
        manualMode: true,
        configLoaded: true,
        features: config.features || {}
      };

    } catch (error) {
      this.logger.error(`Error during data extraction: ${error}`);
      result.errors.push(error instanceof Error ? error.message : String(error));
    }

    return result;
  }

  private async navigateToDocuments(config: ConnectorConfiguration): Promise<void> {
    if (!this.page) return;

    try {
      const navigation = config.navigation;
      const timeouts = config.timeouts;

      // Use configured selectors and URLs
      if (navigation?.documentsUrl) {
        await this.page.goto(config.baseUrl + navigation.documentsUrl);
      } else {
        // Fallback to selector-based navigation
        const documentsSelector = config.selectors.documentsLink ||
          'a[href*="attestation"], a[href*="documents"], a:has-text("Mes attestations")';

        await this.page.waitForSelector(documentsSelector, {
          timeout: timeouts?.navigation || 10000
        });
        await this.page.click(documentsSelector);
      }

      await this.page.waitForLoadState('networkidle');
      this.logger.info('Navigated to documents section using configuration');
    } catch (error) {
      this.logger.warn('Could not automatically navigate to documents section. User can navigate manually.');
    }
  }

  private async displayAvailableDocuments(config: ConnectorConfiguration): Promise<void> {
    if (!this.page) return;

    try {
      // Use configured download button selector
      const downloadSelector = config.selectors.downloadButton ||
        'a[href*=".pdf"], a[download], button:has-text("Télécharger")';

      const downloadLinks = await this.page.locator(downloadSelector).count();

      if (downloadLinks > 0) {
        this.logger.info(`Found ${downloadLinks} potential download links`);

        // Show configured patterns
        this.logger.info(`Supported file types: ${config.downloadPatterns.join(', ')}`);

        // Show naming pattern
        this.logger.info(`Files will be named using pattern: ${config.fileNaming}`);
      } else {
        this.logger.info('No obvious download links found. You may need to navigate manually to find your documents.');
      }
    } catch (error) {
      this.logger.debug('Error while scanning for documents:', String(error));
    }
  }
}
