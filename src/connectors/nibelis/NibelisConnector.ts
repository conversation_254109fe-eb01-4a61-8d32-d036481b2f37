import { BaseConnector } from '../base/BaseConnector';
import { DataExtractionResult } from '../../types';
import { Credentials } from '../base/ConnectorInterface';
import { Logger } from 'winston';
import * as fs from 'fs/promises';

export class NibelisConnector extends BaseConnector {
  name = 'nibelis';
  displayName = 'Nibelis';
  version = '1.0.0';

  constructor(logger: Logger) {
    super(logger);
  }

  async authenticate(credentials?: Credentials): Promise<boolean> {
    if (!this.page) {
      throw new Error('Connector not initialized');
    }

    try {
      await this.page.goto('https://client.nibelis.com/login?APPL=CF');
      await this.page.click('text=Coffre-fort');
      await this.page.waitForLoadState('networkidle');

      if (credentials) {
        const config = this.getConfiguration();
        await this.page.fill(config.selectors.username, credentials.username);
        await this.page.fill(config.selectors.password, credentials.password);
        await this.page.click(config.selectors.loginButton);
        await this.page.waitForLoadState('networkidle');
      } else {
        this.logger.info(`Please complete authentication manually for ${this.displayName}`);
        // Wait for manual login
        await this.page.waitForFunction("!document.location.href.includes('login')", { timeout: 300000 });
      }
      return await this.isAuthenticated();
    } catch (error) {
      this.logger.error(`Authentication failed for ${this.displayName}: ${error}`);
      return false;
    }
  }

  async isAuthenticated(): Promise<boolean> {
    if (!this.page) return false;
    try {
      const logoutLink = this.page.locator('a[href*="GestionDeconnexion"]');
      return await logoutLink.isVisible();
    } catch (error) {
      this.logger.debug(`Error checking authentication status: ${error}`);
      return false;
    }
  }

  async extractData(): Promise<DataExtractionResult> {
    if (!this.page) {
      throw new Error('Connector not initialized');
    }

    const result: DataExtractionResult = {
      success: false,
      downloads: [],
      errors: [],
      metadata: {}
    };

    try {
      const config = this.getConfiguration();
      await this.page.waitForLoadState('networkidle');
      this.logger.info('Starting data extraction for Nibelis...');

      const documentLinks = await this.page.locator(config.selectors.documentLink).all();
      this.logger.info(`Found ${documentLinks.length} documents to download.`);

      for (let i = 0; i < documentLinks.length; i++) {
        try {
            const currentUrl = this.page.url();
            // Re-fetch links on each iteration to avoid stale element reference
            const links = await this.page.locator(config.selectors.documentLink).all();
            const link = links[i];

            const docUrl = await link.getAttribute('href');
            const docText = await link.innerText();

            if (docUrl && docText.trim() && !docText.includes('Déposer un fichier')) {
                this.logger.info(`Navigating to document page: ${docText.trim()}`);
                await this.page.goto(new URL(docUrl, this.page.url()).toString());
                await this.page.waitForLoadState('networkidle');

                const downloadButton = this.page.locator(config.selectors.downloadButton);
                if (await downloadButton.isVisible()) {
                    this.logger.info('Download button found, starting download...');
                    const [download] = await Promise.all([
                        this.page.waitForEvent('download'),
                        downloadButton.click()
                    ]);
                    
                    const path = await download.path();
                    if(path){
                        result.downloads.push({
                            path: path,
                            filename: download.suggestedFilename(),
                            size: (await fs.stat(path)).size
                        });
                        this.logger.info(`Downloaded: ${download.suggestedFilename()}`);
                    }
                } else {
                    this.logger.warn(`Download button not found for document: ${docText.trim()}`);
                }

                await this.page.goto(currentUrl);
                await this.page.waitForLoadState('networkidle');
            }
        } catch (e: unknown) {
          const errorMsg = `Failed to process document ${i + 1}: ${e instanceof Error ? e.message : String(e)}`;
          this.logger.error(errorMsg);
          result.errors.push(errorMsg);
          // Attempt to recover by going back to the main documents page
          await this.page.goto('https://client.nibelis.com/servlet/Gestion?CONVERSATION=Home&ACTION=MODI');
          await this.page.waitForLoadState('networkidle');
        }
      }

      result.success = true;
    } catch (error) {
      this.logger.error(`Error during data extraction: ${error}`);
      result.errors.push(error instanceof Error ? error.message : String(error));
    }

    return result;
  }
}
