import { BaseConnector } from '../base/BaseConnector';
import { DataExtractionResult } from '../../types';
import { Credentials } from '../base/ConnectorInterface';
import { Logger } from 'winston';

export class TemplateConnector extends BaseConnector {
  name = 'template';
  displayName = 'Template Connector';
  version = '1.0.0';

  constructor(logger: Logger) {
    super(logger);
  }

  async authenticate(credentials?: Credentials): Promise<boolean> {
    if (!this.page) {
      throw new Error('Connector not initialized');
    }

    try {
      // Navigate to Template/Demo login page
      await this.page.goto('https://quotes.toscrape.com/login');

      if (credentials) {
        // Fill credentials automatically
        await this.page.fill('#username', credentials.username);
        await this.page.fill('#password', credentials.password);
        await this.page.click('input[type="submit"]');

        // Wait for navigation after login
        await this.page.waitForLoadState('networkidle');
      } else {
        // Manual authentication - wait for user to complete
        this.logger.info(`Please complete authentication manually for ${this.displayName}`);
      }

      // Use the connector-specific isAuthenticated method to verify authentication
      const maxAttempts = 60; // Maximum 5 minutes with 5-second intervals
      let attempts = 0;

      while (attempts < maxAttempts) {
        const authenticated = await this.isAuthenticated();

        if (authenticated) {
          this.logger.info(`Authentication completed for ${this.displayName}`);
          return true;
        }

        // Wait 5 seconds before checking again
        await new Promise(resolve => setTimeout(resolve, 5000));
        attempts++;

        if (attempts % 12 === 0) { // Log every minute
          this.logger.info(`Still waiting for authentication... (${Math.floor(attempts / 12)} minute(s) elapsed)`);
        }
      }

      this.logger.error(`Authentication timeout for ${this.displayName}`);
      return false;

    } catch (error) {
      this.logger.error(`Authentication failed for ${this.displayName}: ${error}`);
      return false;
    }
  }

  async isAuthenticated(): Promise<boolean> {
    if (!this.page) {
      return false;
    }

    try {
      // Check if we're on a login page (indicating we're not authenticated)
      const currentUrl = this.page.url();

      // If we're still on the login URL, we're not authenticated
      if (currentUrl.includes('/login')) {
        return false;
      }

      // Check for specific elements that indicate authentication
      // This should be customized based on the actual service
      const authenticatedIndicators = [
        'a[href*="logout"]'
      ];

      for (const selector of authenticatedIndicators) {
        try {
          const element = await this.page.locator(selector).first();
          if (await element.isVisible({ timeout: 1000 })) {
            return true;
          }
        } catch {
          // Continue to next selector if this one fails
        }
      }

      return false;
    } catch (error) {
      this.logger.debug(`Error checking authentication status: ${error}`);
      return false;
    }
  }

  async extractData(): Promise<DataExtractionResult> {
    if (!this.page) {
      throw new Error('Connector not initialized');
    }

    const result: DataExtractionResult = {
      success: false,
      downloads: [],
      errors: [],
      metadata: {}
    };

    try {
      // Wait for the main page to load after authentication
      await this.page.waitForLoadState('networkidle');

      this.logger.info('Starting data extraction for Ameli...');

      // For MVP, we'll implement a simple manual download assistance
      // Navigate to the documents section
      await this.navigateToDocuments();

      // Show available documents to user
      await this.displayAvailableDocuments();

      // Wait for manual downloads (basic MVP functionality)
      this.logger.info('Please manually download the documents you need.');
      this.logger.info('Session will be saved for future use.');

      // Keep the browser open for manual interaction
      // In a real implementation, we would automate the downloads

      result.success = true;
      result.metadata = {
        sessionSaved: true,
        manualMode: true
      };

    } catch (error) {
      this.logger.error(`Error during data extraction: ${error}`);
      result.errors.push(error instanceof Error ? error.message : String(error));
    }

    return result;
  }

  private async navigateToDocuments(): Promise<void> {
    if (!this.page) return;

    try {
      // Try to find and click on documents/attestations link
      // These selectors may need to be updated based on the actual Ameli interface
      const documentsSelector = 'a[href*="attestation"], a[href*="documents"], a:has-text("Mes attestations")';

      await this.page.waitForSelector(documentsSelector, { timeout: 10000 });
      await this.page.click(documentsSelector);
      await this.page.waitForLoadState('networkidle');

      this.logger.info('Navigated to documents section');
    } catch {
      this.logger.warn('Could not automatically navigate to documents section. User can navigate manually.');
    }
  }

  private async displayAvailableDocuments(): Promise<void> {
    if (!this.page) return;

    try {
      // Look for download links or document listings
      const downloadLinks = await this.page.locator('a[href*=".pdf"], a[download], button:has-text("Télécharger")').count();

      if (downloadLinks > 0) {
        this.logger.info(`Found ${downloadLinks} potential download links`);
      } else {
        this.logger.info('No obvious download links found. You may need to navigate manually to find your documents.');
      }
    } catch (error) {
      this.logger.debug('Error while scanning for documents:', error);
    }
  }
}
