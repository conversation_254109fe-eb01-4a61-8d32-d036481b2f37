/**
 * This script is executed in the browser context to extract video data from the YouTube "Watch Later" playlist.
 * It is not meant to be run directly in Node.js.
 */

// Configuration for CSS selectors, to make them easier to update
const SELECTORS = {
  videoRenderer: 'ytd-playlist-video-renderer',
  title: '#video-title',
  channelLink: '#text a[href*="/channel/"], #text a[href*="/@"]',
  duration: '#overlays span.ytd-thumbnail-overlay-time-status-renderer',
  videoLink: 'a[href*="/watch"]',
  thumbnail: 'img[src*="ytimg.com"]',
  progress: [
    'ytd-thumbnail-overlay-resume-playback-renderer #progress',
    '#progress',
    'ytd-thumbnail-overlay-resume-playback-renderer div[style*="width"]',
    '.ytd-thumbnail-overlay-resume-playback-renderer #progress'
  ],
  views: [
    '#metadata span',
    'ytd-video-meta-block span',
    '#metadata-line span',
    '.style-scope.ytd-video-meta-block span'
  ],
  metadataSpans: '#metadata span, ytd-video-meta-block span, #metadata-line span',
  playlistMetadata: 'ytd-playlist-video-renderer #metadata-container span'
};

// Helper function to query an element and return null if not found
function query(element, selector) {
  return element.querySelector(selector);
}

// Helper function to query multiple elements and return an array
function queryAll(element, selector) {
  return Array.from(element.querySelectorAll(selector));
}

// Helper function to convert "1:23" or "1:23:45" duration to milliseconds
function convertDurationToMilliseconds(durationString) {
  if (!durationString || !durationString.trim()) return 0;
  const parts = durationString.trim().split(':').map(part => parseInt(part, 10));
  if (parts.some(isNaN)) return 0;

  let totalSeconds = 0;
  if (parts.length === 2) { // mm:ss
    totalSeconds = parts[0] * 60 + parts[1];
  } else if (parts.length === 3) { // hh:mm:ss
    totalSeconds = parts[0] * 3600 + parts[1] * 60 + parts[2];
  }
  return totalSeconds * 1000;
}

// Helper function to convert relative dates like "2 days ago" to an ISO string
function convertRelativeDateToISO(relativeDate) {
  if (!relativeDate || !relativeDate.trim()) return undefined;

  const now = new Date();
  const text = relativeDate.toLowerCase().trim();

  const patterns = [
    { regex: /il y a (\d+) min|(\d+) min ago/i, unit: 'minutes' },
    { regex: /il y a (\d+) heure|(\d+) hour ago/i, unit: 'hours' },
    { regex: /il y a (\d+) jour|(\d+) day ago/i, unit: 'days' },
    { regex: /il y a (\d+) sem|(\d+) week ago/i, unit: 'weeks' },
    { regex: /il y a (\d+) mois|(\d+) month ago/i, unit: 'months' },
    { regex: /il y a (\d+) an|(\d+) year ago/i, unit: 'years' },
  ];

  for (const { regex, unit } of patterns) {
    const match = text.match(regex);
    if (match) {
      const value = parseInt(match[1] || match[2], 10);
      if (isNaN(value)) continue;

      const date = new Date(now);
      if (unit === 'minutes') date.setMinutes(date.getMinutes() - value);
      else if (unit === 'hours') date.setHours(date.getHours() - value);
      else if (unit === 'days') date.setDate(date.getDate() - value);
      else if (unit === 'weeks') date.setDate(date.getDate() - (value * 7));
      else if (unit === 'months') date.setMonth(date.getMonth() - value);
      else if (unit === 'years') date.setFullYear(date.getFullYear() - value);
      return date.toISOString();
    }
  }

  if (text.includes('à l\'instant') || text.includes('just now') || text.includes('maintenant')) {
    return now.toISOString();
  }

  return undefined; // Return undefined if no pattern matches
}

// Helper function to parse view counts like "1.2K views" or "1,2 M de vues"
function parseViewCount(viewString) {
  if (!viewString || !viewString.trim()) return 0;

  const cleanString = viewString.toLowerCase()
    .replace(/vues?|views?|de/g, '')
    .replace(/\s+/g, '')
    .replace(',', '.');

  const match = cleanString.match(/([0-9.]+)([kmgtb]?)/);
  if (!match) return 0;

  const numberPart = parseFloat(match[1]);
  const suffix = match[2];
  if (isNaN(numberPart)) return 0;

  let multiplier = 1;
  if (suffix === 'k') multiplier = 1e3;
  else if (suffix === 'm') multiplier = 1e6;
  else if (suffix === 'b' || suffix === 'g') multiplier = 1e9; // Billions
  else if (suffix === 't') multiplier = 1e12; // Trillions

  return Math.round(numberPart * multiplier);
}

// Main extraction function
function extractVideos() {
  const videoElements = queryAll(document, SELECTORS.videoRenderer);
  const results = [];
  const currentTime = new Date().toISOString();

  for (let i = 0; i < videoElements.length; i++) {
    try {
      const element = videoElements[i];

      const titleEl = query(element, SELECTORS.title);
      const channelEl = query(element, SELECTORS.channelLink);
      const durationEl = query(element, SELECTORS.duration);
      const linkEl = query(element, SELECTORS.videoLink);
      const thumbnailEl = query(element, SELECTORS.thumbnail);

      // Extract watch progress
      let progressPercentage = 0;
      for (const selector of SELECTORS.progress) {
        const progressElement = query(element, selector);
        if (progressElement) {
          const styleAttr = progressElement.getAttribute('style') || '';
          const widthMatch = styleAttr.match(/width:\s*([0-9.]+)%/);
          if (widthMatch) {
            const progressValue = parseFloat(widthMatch[1]);
            progressPercentage = progressValue >= 99.5 ? 0 : progressValue; // 0 if fully watched
            break;
          }
        }
      }

      // Extract views
      let viewsEl;
      for (const selector of SELECTORS.views) {
        viewsEl = query(element, selector);
        if (viewsEl) break;
      }

      // Extract dates
      const metadataSpans = queryAll(element, SELECTORS.metadataSpans);
      let publishedDateEl = null;
      let addedDateEl = null;

      for (const span of metadataSpans) {
        const spanText = span.textContent?.trim() || '';
        if (!publishedDateEl && spanText.match(/il y a|ago|depuis|publiée|published/i)) {
          publishedDateEl = span;
        }
        if (!addedDateEl && spanText.match(/ajoutée|added|enregistrée/i)) {
          addedDateEl = span;
        }
      }

      const playlistMetaEl = query(element, SELECTORS.playlistMetadata);
      if (playlistMetaEl && !addedDateEl && (playlistMetaEl.textContent?.trim() || '').match(/ajoutée|added|enregistrée/i)) {
        addedDateEl = playlistMetaEl;
      }

      const title = titleEl?.textContent?.trim() || '';
      const href = linkEl?.href || '';

      if (title && href) {
        const videoIdMatch = href.match(/[?&]v=([^&]+)/);
        const videoId = videoIdMatch ? videoIdMatch[1] : '';

        const channelHref = channelEl?.href || '';
        let channelId = '';
        if (channelHref) {
          const channelIdMatch = channelHref.match(/\/channel\/([^/?]+)/);
          const usernameMatch = channelHref.match(/\/@([^/?]+)/);
          if (channelIdMatch) channelId = channelIdMatch[1];
          else if (usernameMatch) channelId = '@' + usernameMatch[1];
        }

        results.push({
          index: i + 1,
          title,
          channel: channelEl?.textContent?.trim() || '',
          channelId,
          duration: convertDurationToMilliseconds(durationEl?.textContent?.trim()),
          views: parseViewCount(viewsEl?.textContent?.trim()),
          url: href,
          thumbnail: thumbnailEl?.src || '',
          videoId,
          extractedAt: currentTime,
          publishedDate: convertRelativeDateToISO(publishedDateEl?.textContent?.trim()) || publishedDateEl?.textContent?.trim() || undefined,
          addedToWatchLaterDate: convertRelativeDateToISO(addedDateEl?.textContent?.trim()) || addedDateEl?.textContent?.trim() || undefined,
          progress: progressPercentage,
        });
      }
    } catch (error) {
      // In browser context, we can't use a logger, so we log to the console.
      console.warn(`Error extracting video at index ${i}:`, error);
    }
  }
  return results;
}

// Execute the function immediately and return the result
// Using an IIFE (Immediately Invoked Function Expression) to avoid top-level return
(() => {
  return extractVideos();
})();