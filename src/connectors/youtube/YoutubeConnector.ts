import { BaseConnector } from '../base/BaseConnector';
import { DataExtractionResult } from '../../types';
import { Logger } from 'winston';
import * as fs from 'fs';
import * as path from 'path';

interface VideoData {
  index: number;
  title: string;
  channel: string;
  channelId: string;
  duration: number; // Duration in milliseconds
  views: number; // Number of views
  url: string;
  thumbnail: string;
  videoId: string;
  extractedAt: string;
  publishedDate?: string; // Date when the video was published (ISO format when possible, fallback to original text)
  addedToWatchLaterDate?: string; // Date when the video was added to Watch Later (ISO format when possible, fallback to original text)
  progress: number; // Watch progress as percentage (0-100), 0 if fully watched or no progress
}

interface PlaylistData {
  title: string;
  totalVideos: number;
  extractedAt: string;
  videos: VideoData[];
}

// Centralized CSS selectors for easier maintenance
const SELECTORS = {
  // Selectors for authentication check
  auth: {
    avatar: [
      'button[aria-label*="compte Google"]',
      'img[alt="Avatar"]',
      '#avatar-btn',
    ],
    accountMenu: 'ytd-topbar-menu-button-renderer[has-avatar]',
    userChannel: 'a[href*="/channel/"]',
    library: [
      'ytd-guide-entry-renderer[aria-label*="Bibliothèque"]',
      'ytd-guide-entry-renderer[aria-label*="Library"]',
    ],
  },
  // Selectors for Watch Later playlist
  watchLater: {
    playlistHeader: 'ytd-playlist-header-renderer',
    playlistTitle: 'h1.ytd-playlist-header-renderer, h1',
    videoRenderer: 'ytd-playlist-video-renderer',
    videoListRenderer: 'ytd-playlist-video-list-renderer',
    watchLaterTitle: [
      'h1:has-text("À regarder plus tard")',
      'h1:has-text("Watch later")',
    ],
  },
};

export class YoutubeConnector extends BaseConnector {
  name = 'youtube';
  displayName = 'YouTube';
  version = '1.0.0';

  private youtubeExtractorScript: string;

  constructor(logger: Logger) {
    super(logger);
    // Load the external extractor script
    this.youtubeExtractorScript = fs.readFileSync(
      path.join(__dirname, 'youtubeExtractor.js'),
      'utf8'
    );
  }

  async authenticate(): Promise<boolean> {
    if (!this.page) {
      throw new Error('Connector not initialized');
    }

    try {
      // Navigate to Google Sign In for YouTube
      await this.page.goto('https://accounts.google.com/signin/v2/identifier?continue=https%3A%2F%2Fwww.youtube.com%2Fsignin%3Faction_handle_signin%3Dtrue%26app%3Ddesktop&flowName=GlifWebSignIn&flowEntry=ServiceLogin');

      this.logger.info(`Please complete Google authentication manually for ${this.displayName}`);
      this.logger.info('Once signed in, you will be redirected to YouTube automatically');

      // Use the connector-specific isAuthenticated method to verify authentication
      const maxAttempts = 60; // Maximum 5 minutes with 5-second intervals
      let attempts = 0;

      while (attempts < maxAttempts) {
        if (await this.isAuthenticated()) {
          this.logger.info(`Authentication completed for ${this.displayName}`);
          return true;
        }

        // Wait 5 seconds before checking again
        await new Promise(resolve => setTimeout(resolve, 5000));
        attempts++;

        if (attempts % 12 === 0) { // Log every minute
          this.logger.info(`Still waiting for authentication... (${Math.floor(attempts / 12)} minute(s) elapsed)`);
        }
      }

      this.logger.error(`Authentication timeout for ${this.displayName}`);
      return false;

    } catch (error) {
      this.logger.error(`Authentication failed for ${this.displayName}: ${error}`);
      return false;
    }
  }

  async isAuthenticated(): Promise<boolean> {
    if (!this.page) return false;

    try {
      // Don't check authentication if we're currently on a Google sign-in page
      // This prevents interrupting the authentication process
      const currentUrl = this.page.url();
      if (currentUrl.includes('accounts.google.com') || 
          currentUrl.includes('signin') || 
          currentUrl.includes('oauth') ||
          currentUrl.includes('login')) {
        this.logger.debug('Currently on authentication page, skipping auth check');
        return false;
      }

      // If we're not on YouTube, navigate there first without the timeout that could interrupt auth
      if (!currentUrl.includes('youtube.com')) {
        try {
          await this.page.goto('https://www.youtube.com/', {
            waitUntil: 'networkidle',
            timeout: 15000
          });
        } catch (error) {
          this.logger.debug(`Could not navigate to YouTube: ${error}`);
          return false;
        }
      }

      // Combine all authentication selectors into one array
      const authSelectors = [
        ...SELECTORS.auth.avatar,
        SELECTORS.auth.accountMenu,
        SELECTORS.auth.userChannel,
        ...SELECTORS.auth.library,
      ];

      // Check if any primary authentication indicator is present
      // Use a longer timeout to give more time for page loading
      for (const selector of authSelectors) {
        try {
          const element = await this.page.locator(selector).first();
          if (await element.isVisible({ timeout: 5000 })) {
            this.logger.debug(`Authentication confirmed: found element ${selector}`);
            return true;
          }
        } catch { /* Continue to next selector */ }
      }

      // Additional check: look for any signed-in user indicators
      // without navigating to a specific page
      try {
        const signedInSelectors = [
          'button[aria-label*="Google Account"]',
          'img[alt*="Avatar"]',
          '#avatar-btn img',
          'ytd-topbar-menu-button-renderer img',
          '#guide [role="button"] img',
          'yt-img-shadow img[src*="googleusercontent"]'
        ];

        for (const selector of signedInSelectors) {
          try {
            const element = await this.page.locator(selector).first();
            if (await element.isVisible({ timeout: 3000 })) {
              this.logger.debug(`Authentication confirmed: found signed-in indicator ${selector}`);
              return true;
            }
          } catch { /* Continue to next selector */ }
        }
      } catch { /* Fallback failed, user probably not authenticated */ }

      // Final check: try to access Watch Later page ONLY if we haven't found other indicators
      // and we're not in the middle of an authentication flow
      if (!currentUrl.includes('signin') && !currentUrl.includes('accounts.google.com')) {
        try {
          await this.page.goto('https://www.youtube.com/playlist?list=WL', {
            waitUntil: 'networkidle',
            timeout: 15000  // Increased timeout
          });

          // Check if we can see the Watch Later playlist title or header
          const watchLaterSelectors = [
            ...SELECTORS.watchLater.watchLaterTitle,
            SELECTORS.watchLater.playlistHeader,
          ];

          for (const selector of watchLaterSelectors) {
            try {
              const element = await this.page.locator(selector).first();
              if (await element.isVisible({ timeout: 5000 })) {
                this.logger.debug('Authentication confirmed: can access Watch Later playlist');
                return true;
              }
            } catch { /* Continue */ }
          }
        } catch { /* Failed to access Watch Later, likely not authenticated */ }
      }

      return false;
    } catch (error) {
      this.logger.debug(`Error checking YouTube authentication status: ${error}`);
      return false;
    }
  }

  async extractData(): Promise<DataExtractionResult> {
    if (!this.page) {
      throw new Error('Connector not initialized');
    }

    const result: DataExtractionResult = {
      success: false,
      downloads: [],
      errors: [],
      metadata: {}
    };

    const overallStartTime = Date.now();

    try {
      this.logger.info('Starting Watch Later playlist extraction for YouTube...');

      const navigationStartTime = Date.now();
      await this.navigateToWatchLater();
      const navigationTime = Date.now() - navigationStartTime;
      this.logger.info(`Navigation completed in ${navigationTime}ms (${(navigationTime / 1000).toFixed(2)}s)`);

      const extractionStartTime = Date.now();
      const playlistData = await this.extractWatchLaterData();
      const extractionTime = Date.now() - extractionStartTime;
      this.logger.info(`Data extraction completed in ${extractionTime}ms (${(extractionTime / 1000).toFixed(2)}s)`);

      const saveStartTime = Date.now();
      const saveResult = await this.saveData('youtube-watch-later', playlistData.videos, {
        primaryKey: 'videoId', // Use videoId as the primary key for merging
        mergeStrategy: 'sync-preserve-nonnull', // Synchronize and preserve non-null old values
        updateTimestamp: true,
        preserveHistory: true,
        metadata: {
          totalVideos: playlistData.videos.length,
          playlistTitle: playlistData.title,
          playlistExtractedAt: playlistData.extractedAt
        }
      });
      const saveTime = Date.now() - saveStartTime;
      this.logger.info(`File save completed in ${saveTime}ms (${(saveTime / 1000).toFixed(2)}s)`);

      const overallTime = Date.now() - overallStartTime;
      this.logTimingSummary(navigationTime, extractionTime, saveTime, overallTime);

      result.success = saveResult.success;
      if (saveResult.success && saveResult.filePath) {
        result.downloads = [{
          filename: path.basename(saveResult.filePath),
          path: saveResult.filePath,
          size: saveResult.size
        }];
      }
      result.metadata = {
        totalVideos: playlistData.videos.length,
        extractedAt: new Date().toISOString(),
        playlistTitle: playlistData.title,
        timingStats: {
          navigationTimeMs: navigationTime,
          extractionTimeMs: extractionTime,
          saveTimeMs: saveTime,
          totalTimeMs: overallTime
        },
        saveResult: {
          success: saveResult.success,
          timestamp: saveResult.timestamp,
          error: saveResult.error
        }
      };

      this.logger.info(`Successfully extracted ${playlistData.videos.length} videos from Watch Later playlist`);

    } catch (error) {
      this.logger.error(`Error during YouTube data extraction: ${error}`);
      result.errors.push(error instanceof Error ? error.message : String(error));
    }

    return result;
  }

  private async navigateToWatchLater(): Promise<void> {
    if (!this.page) return;

    try {
      this.logger.info('Navigating to Watch Later playlist...');
      await this.page.goto('https://www.youtube.com/playlist?list=WL', {
        waitUntil: 'networkidle',
        timeout: 30000
      });

      // Wait for either a video renderer or the list renderer to appear
      await this.page.waitForSelector(
        `${SELECTORS.watchLater.videoRenderer}, ${SELECTORS.watchLater.videoListRenderer}`,
        { timeout: 15000 }
      );

      this.logger.info('Successfully navigated to Watch Later playlist');
    } catch (error) {
      throw new Error(`Failed to navigate to Watch Later playlist: ${error}`);
    }
  }

  private async extractWatchLaterData(): Promise<PlaylistData> {
    if (!this.page) throw new Error('Page not initialized');

    this.logger.info('Extracting Watch Later playlist data...');

    const titleStartTime = Date.now();
    const title = await this.page.locator(SELECTORS.watchLater.playlistTitle).first().textContent() || 'Watch Later';
    const titleTime = Date.now() - titleStartTime;
    this.logger.debug(`Title extraction: ${titleTime}ms`);

    const scrollStartTime = Date.now();
    await this.scrollToLoadAllVideos();
    const scrollTime = Date.now() - scrollStartTime;
    this.logger.info(`Scrolling/loading completed in ${scrollTime}ms (${(scrollTime / 1000).toFixed(2)}s)`);

    const extractionStartTime = Date.now();
    const videos = await this.extractVideoElements();
    const extractionTime = Date.now() - extractionStartTime;
    this.logger.info(`Video elements extraction completed in ${extractionTime}ms (${(extractionTime / 1000).toFixed(2)}s)`);

    this.logger.info(`Extracted ${videos.length} videos from playlist`);
    this.logExtractionSubTiming(titleTime, scrollTime, extractionTime);

    return {
      title: title.trim(),
      totalVideos: videos.length,
      extractedAt: new Date().toISOString(),
      videos: videos
    };
  }

  private async extractVideoElements(): Promise<VideoData[]> {
    if (!this.page) return [];

    this.logger.info('Using optimized bulk extraction method...');
    const extractionStartTime = Date.now();

    // Execute the external script in the browser context
    const videos: VideoData[] = await this.page.evaluate(this.youtubeExtractorScript);

    const extractionTime = Date.now() - extractionStartTime;
    const averageTimePerVideo = videos.length > 0 ? extractionTime / videos.length : 0;

    this.logger.info(`Optimized extraction completed: ${videos.length} videos in ${extractionTime}ms`);
    this.logger.info(`Performance: ${averageTimePerVideo.toFixed(0)}ms per video (${(extractionTime / 1000).toFixed(2)}s total)`);

    // Log extraction statistics and potential issues
    await this.logExtractionStats(videos);

    return videos;
  }

  private async scrollToLoadAllVideos(): Promise<void> {
    if (!this.page) return;

    this.logger.info('Scrolling to load all videos in playlist...');

    let previousVideoCount = 0;
    let currentVideoCount = 0;
    let noChangeCount = 0;
    let scrollIterations = 0;
    const scrollStartTime = Date.now();

    do {
      const iterationStartTime = Date.now();
      previousVideoCount = currentVideoCount;

      await this.page.keyboard.press('End');
      await this.page.waitForTimeout(1000); // Wait for content to load

      currentVideoCount = await this.page.locator(SELECTORS.watchLater.videoRenderer).count();

      if (currentVideoCount === previousVideoCount) {
        noChangeCount++;
      } else {
        noChangeCount = 0;
        const iterationTime = Date.now() - iterationStartTime;
        this.logger.debug(`Loaded ${currentVideoCount} videos so far... (iteration took ${iterationTime}ms)`);
      }

      await this.page.waitForTimeout(Math.random() * 1000 + 500); // Human-like delay
      scrollIterations++;

    } while (noChangeCount < 3); // Stop if no new videos load after 3 attempts

    const totalScrollTime = Date.now() - scrollStartTime;
    this.logger.info(`Finished loading playlist. Total videos: ${currentVideoCount}`);
    this.logger.info(`Scrolling statistics: ${scrollIterations} iterations, average ${(totalScrollTime / scrollIterations).toFixed(0)}ms per iteration`);
  }



  // Helper methods for logging to keep other methods cleaner
  private logTimingSummary(navTime: number, extractTime: number, saveTime: number, totalTime: number): void {
    this.logger.info(`=== TIMING SUMMARY ===`);
    this.logger.info(`Navigation: ${navTime}ms (${((navTime / totalTime) * 100).toFixed(1)}%)`);
    this.logger.info(`Data extraction: ${extractTime}ms (${((extractTime / totalTime) * 100).toFixed(1)}%)`);
    this.logger.info(`File save: ${saveTime}ms (${((saveTime / totalTime) * 100).toFixed(1)}%)`);
    this.logger.info(`Total time: ${totalTime}ms (${(totalTime / 1000).toFixed(2)}s)`);
    this.logger.info(`======================`);
  }

  private logExtractionSubTiming(titleTime: number, scrollTime: number, extractionTime: number): void {
    const total = titleTime + scrollTime + extractionTime;
    this.logger.info(`--- Sub-timing within data extraction ---`);
    this.logger.info(`Title: ${titleTime}ms (${((titleTime / total) * 100).toFixed(1)}%)`);
    this.logger.info(`Scrolling: ${scrollTime}ms (${((scrollTime / total) * 100).toFixed(1)}%)`);
    this.logger.info(`Elements extraction: ${extractionTime}ms (${((extractionTime / total) * 100).toFixed(1)}%)`);
  }

  private async logExtractionStats(videos: VideoData[]): Promise<void> {
    if (!this.page) return;
    const totalElements = await this.page.locator(SELECTORS.watchLater.videoRenderer).count();
    if (videos.length < totalElements) {
      this.logger.warn(`Extracted ${videos.length}/${totalElements} videos. ${totalElements - videos.length} videos may have been skipped.`);
    }

    const withPublishedDate = videos.filter(v => v.publishedDate).length;
    const withAddedDate = videos.filter(v => v.addedToWatchLaterDate).length;
    const withProgress = videos.filter(v => v.progress > 0).length;
    const fullyWatched = videos.filter(v => v.progress === 0).length;

    this.logger.info(`Date extraction: ${withPublishedDate}/${videos.length} with publication date, ${withAddedDate}/${videos.length} with added date.`);
    this.logger.info(`Progress extraction: ${withProgress}/${videos.length} with progress, ${fullyWatched}/${videos.length} fully watched or not started.`);

    if (withPublishedDate === 0) this.logger.warn('No publication dates found.');
    if (withAddedDate === 0) this.logger.warn('No "added to Watch Later" dates found.');
    if (withProgress === 0) this.logger.warn('No watch progress found.');
  }
}
