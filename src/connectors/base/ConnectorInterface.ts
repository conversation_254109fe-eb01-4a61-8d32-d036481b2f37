import { ConnectorConfiguration, ConnectorContext, DataExtractionResult } from '../../types';

// Base interface that all connectors must implement
export interface IConnector {
  name: string;
  displayName: string;
  version: string;

  // Lifecycle methods
  initialize(context: ConnectorContext): Promise<void>;
  authenticate(credentials?: Credentials): Promise<boolean>;
  isAuthenticated(): Promise<boolean>;
  extractData(): Promise<DataExtractionResult>;
  cleanup(): Promise<void>;

  // Configuration
  getConfiguration(): ConnectorConfiguration;
  validateConfiguration(config: ConnectorConfiguration): boolean;

  // Session management
  saveSession(): Promise<any>;
}

// Credentials interface
export interface Credentials {
  username: string;
  password: string;
  [key: string]: string;
}
