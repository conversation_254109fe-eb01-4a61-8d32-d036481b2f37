import { chromium, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontex<PERSON>, <PERSON> } from 'patchright';
import { IConnector, Credentials } from './ConnectorInterface';
import { ConnectorConfiguration, ConnectorContext, DataExtractionResult, ProfileData, SaveDataOptions, SaveDataResult, DataStorageConfiguration } from '../../types';
import { Logger } from 'winston';
import { ProfileManager } from '../../session/ProfileManager';
import { DataStorageManager } from '../../storage/DataStorageManager';
import * as fs from 'fs';
import * as path from 'path';

export abstract class BaseConnector implements IConnector {
  protected browser: Browser | null = null;
  protected context: BrowserContext | null = null;
  protected page: Page | null = null;
  protected logger: Logger;
  protected connectorContext!: ConnectorContext;
  protected profileManager: ProfileManager | null = null;
  protected currentProfile: ProfileData | null = null;
  private configuration: ConnectorConfiguration | null = null;
  private dataStorageManager: DataStorageManager | null = null;

  abstract name: string;
  abstract displayName: string;
  abstract version: string;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  // Method to securely save credentials
  protected async saveCredentials(credentials: Credentials): Promise<void> {
    if (!this.connectorContext.masterPassword) {
      this.logger.warn('Master password is not set. Credentials will not be saved.');
      return;
    }

    this.logger.info(`Saving credentials for ${this.name}`);
    const encryptedCredentials = await this.connectorContext.securityManager.encrypt(
      JSON.stringify(credentials),
      this.connectorContext.masterPassword
    );

    // TODO: For now, we store this in the connector's config file.
    // In the future, this should be in a dedicated, secure storage.
    const configPath = path.join('./tomodata-data', 'config', `${this.name}.json`);
    const config = fs.existsSync(configPath) ? JSON.parse(fs.readFileSync(configPath, 'utf-8')) : {};
    config.credentials = encryptedCredentials;
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    this.logger.info(`Credentials securely saved for ${this.name}`);
  }

  // Method to load and decrypt credentials
  protected async loadCredentials(): Promise<Credentials | null> {
    if (!this.connectorContext.masterPassword) {
      this.logger.warn('Master password is not set. Cannot load credentials.');
      return null;
    }

    // TODO: Load from the connector's config file.
    const configPath = path.join('./tomodata-data', 'config', `${this.name}.json`);
    if (!fs.existsSync(configPath)) {
      return null;
    }

    const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
    const encryptedCredentials = config.credentials;

    if (!encryptedCredentials) {
      return null;
    }

    this.logger.info(`Loading credentials for ${this.name}`);
    const decryptedJson = await this.connectorContext.securityManager.decrypt(
      encryptedCredentials,
      this.connectorContext.masterPassword
    );

    return JSON.parse(decryptedJson) as Credentials;
  }

  async initialize(context: ConnectorContext): Promise<void> {
    this.connectorContext = context;

    // Initialize profile manager if using profiles
    if (context.useProfile) {
      // Create profile manager using the base data directory
      const baseDataDir = path.resolve('./tomodata-data');
      this.profileManager = new ProfileManager(baseDataDir, this.logger);

      // Get or create profile for this connector
      this.currentProfile = await this.profileManager.getOrCreateProfile(this.name);

      // Launch browser with persistent context (profile)
      this.context = await chromium.launchPersistentContext(
        this.currentProfile.profilePath,
        {
          headless: context.headless,
          devtools: context.debug,
          args: [ // for wayland
            "--ozone-platform-hint=auto",
            "--enable-features=AcceleratedVideoDecodeLinuxGL",
            "--use-gl=angle",
            "--use-angle=vulkan",
          ],
          ignoreDefaultArgs: ["--enable-automation"], // No "controlled by automation" infobar
          chromiumSandbox: false,
          channel: 'chromium',
          acceptDownloads: true
        }
      );

      this.logger.info(`${this.displayName} connector initialized with persistent profile: ${this.currentProfile.profilePath}`);
    } else {
      // Fallback to old session-based approach
      this.browser = await chromium.launch({
        headless: context.headless,
        devtools: context.debug,
        args: [ // for wayland
          "--ozone-platform-hint=auto",
          "--enable-features=AcceleratedVideoDecodeLinuxGL",
          "--use-gl=angle",
          "--use-angle=vulkan",
        ],
        ignoreDefaultArgs: ["--enable-automation"], // No "controlled by automation" infobar
        chromiumSandbox: false,
        channel: 'chromium'
      });

      // Create browser context
      this.context = await this.browser.newContext({
        acceptDownloads: true
      });

      // Restore session if available
      if (context.sessionData) {
        await this.restoreSession();
      }

      this.logger.info(`${this.displayName} connector initialized with temporary session`);
    }

    this.page = await this.context.newPage();

    // Initialize data storage manager
    await this.initializeDataStorage();
  }

  private async initializeDataStorage(): Promise<void> {
    try {
      // Load global configuration for data storage
      const globalConfigPath = path.join('./tomodata-data', 'config', 'config.json');
      let dataStorageConfig: DataStorageConfiguration;

      if (fs.existsSync(globalConfigPath)) {
        const globalConfig = JSON.parse(fs.readFileSync(globalConfigPath, 'utf8'));
        dataStorageConfig = globalConfig.global?.dataStorage || this.getDefaultDataStorageConfig();
      } else {
        dataStorageConfig = this.getDefaultDataStorageConfig();
      }

      this.dataStorageManager = new DataStorageManager(dataStorageConfig, this.logger);
      await this.dataStorageManager.initialize();

      this.logger.debug(`Data storage initialized for ${this.displayName} connector`);
    } catch (error) {
      this.logger.error(`Failed to initialize data storage for ${this.displayName}: ${error}`);
      // Use default JSON storage as fallback
      this.dataStorageManager = new DataStorageManager(this.getDefaultDataStorageConfig(), this.logger);
      await this.dataStorageManager.initialize();
    }
  }

  private getDefaultDataStorageConfig(): DataStorageConfiguration {
    return {
      type: 'json',
      options: {
        baseDirectory: './tomodata-data/downloads',
        prettyPrint: true,
        compression: false,
        backupEnabled: false,
        retentionDays: 365
      }
    };
  }

  // Abstract method that each connector must implement for authentication
  abstract authenticate(credentials?: Credentials): Promise<boolean>;

  // Abstract method that each connector must implement to check authentication status
  abstract isAuthenticated(): Promise<boolean>;

  abstract extractData(): Promise<DataExtractionResult>;

  getConfiguration(): ConnectorConfiguration {
    if (!this.configuration) {
      this.configuration = this.loadConfiguration();
    }
    return this.configuration;
  }

  private loadConfiguration(): ConnectorConfiguration {
    try {
      const configPath = this.getConfigPath();

      if (!fs.existsSync(configPath)) {
        throw new Error(`Configuration file not found: ${configPath}. Please create a config.json file for the ${this.name} connector.`);
      }

      const configData = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configData);

      this.logger.debug(`Loaded configuration from ${configPath} for ${this.name}`);
      return config;
    } catch (error) {
      if (error instanceof Error && error.message.includes('Configuration file not found')) {
        // Re-throw our custom error as-is
        throw error;
      }
      // For other errors (JSON parsing, file reading, etc.)
      throw new Error(`Failed to load configuration for ${this.name} connector: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private getConfigPath(): string {
    // Use the connector name to find the config file
    // This assumes the connector is in src/connectors/{name}/config.json
    const srcDir = path.resolve(__dirname, '../../');
    return path.join(srcDir, 'connectors', this.name, 'config.json');
  }

  validateConfiguration(config: ConnectorConfiguration): boolean {
    return !!(config.name && config.displayName && config.baseUrl);
  }

  async cleanup(): Promise<void> {
    if (this.page) {
      await this.page.close();
    }

    if (this.context) {
      await this.context.close();
    }

    if (this.browser) {
      await this.browser.close();
    }

    // Cleanup data storage manager
    if (this.dataStorageManager) {
      await this.dataStorageManager.cleanup();
    }

    // Deactivate profile if we were using one
    if (this.profileManager && this.currentProfile) {
      await this.profileManager.deactivateProfile(this.currentProfile.connectorName);
    }

    this.logger.info(`${this.displayName} connector cleaned up`);
  }

  // Helper method to save session data
  async saveSession(): Promise<object | null> {
    if (!this.context) return null;

    const cookies = await this.context.cookies();
    const sessionData = {
      cookies,
      localStorage: {},
      sessionStorage: {},
      timestamp: new Date()
    };

    return sessionData;
  }

  // Helper method to restore session data
  private async restoreSession(): Promise<void> {
    if (!this.context || !this.connectorContext.sessionData) return;

    const { cookies } = this.connectorContext.sessionData;
    if (cookies && cookies.length > 0) {
      await this.context.addCookies(cookies);
      this.logger.info(`Session restored for ${this.displayName}`);
    }
  }

  // Helper method for downloading files
  protected async downloadFile(downloadPromise: Promise<{ saveAs: (path: string) => Promise<void> }>, filename: string): Promise<string> {
    const downloadPath = this.connectorContext.downloadPath;
    const download = await downloadPromise;
    const filePath = `${downloadPath}/${filename}`;

    await download.saveAs(filePath);
    this.logger.info(`File downloaded: ${filename}`);

    return filePath;
  }

  // Central data saving method
  protected async saveData(doctype: string, data: unknown, options: SaveDataOptions = {}): Promise<SaveDataResult> {
    if (!this.dataStorageManager) {
      throw new Error('Data storage manager not initialized. Ensure connector is properly initialized.');
    }

    // Add connector metadata to options
    const enhancedOptions: SaveDataOptions = {
      ...options,
      metadata: {
        connectorName: this.name,
        connectorVersion: this.version,
        extractedBy: this.displayName,
        ...options.metadata
      }
    };

    this.logger.info(`Saving data for doctype: ${doctype} from ${this.displayName} connector`);
    
    const result = await this.dataStorageManager.saveData(doctype, data, enhancedOptions);
    
    if (result.success) {
      this.logger.info(`Data successfully saved: ${result.filePath || result.tableId} (${result.size} bytes)`);
    } else {
      this.logger.error(`Failed to save data: ${result.error}`);
    }

    return result;
  }
}
