import * as fs from 'fs-extra';
import * as path from 'path';
import { ProfileData } from '../types';
import { Logger } from 'winston';

export class ProfileManager {
  private profilesDir: string;
  private logger: Logger;
  private activeProfiles: Map<string, ProfileData> = new Map();

  constructor(baseDir: string, logger: Logger) {
    // Create profiles directory under the base data directory
    this.profilesDir = path.join(baseDir, 'profiles');
    this.logger = logger;
    this.ensureProfilesDirectory();
  }

  private ensureProfilesDirectory(): void {
    if (!fs.existsSync(this.profilesDir)) {
      fs.mkdirSync(this.profilesDir, { recursive: true });
      this.logger.debug(`Created profiles directory: ${this.profilesDir}`);
    }
  }

  /**
   * Get the profile path for a specific connector
   */
  getProfilePath(connectorName: string): string {
    return path.join(this.profilesDir, connectorName);
  }

  /**
   * Get the profile metadata file path for a connector
   */
  private getProfileMetadataPath(connectorName: string): string {
    return path.join(this.profilesDir, `${connectorName}-profile.json`);
  }

  /**
   * Create or get existing profile for a connector
   */
  async getOrCreateProfile(connectorName: string): Promise<ProfileData> {
    // Check if we already have this profile loaded
    if (this.activeProfiles.has(connectorName)) {
      const profile = this.activeProfiles.get(connectorName)!;
      profile.lastUsed = new Date();
      await this.saveProfileMetadata(profile);
      return profile;
    }

    // Try to load existing profile
    const existingProfile = await this.loadProfileMetadata(connectorName);
    if (existingProfile) {
      existingProfile.lastUsed = new Date();
      existingProfile.isActive = true;
      this.activeProfiles.set(connectorName, existingProfile);
      await this.saveProfileMetadata(existingProfile);
      return existingProfile;
    }

    // Create new profile
    const profilePath = this.getProfilePath(connectorName);
    await fs.ensureDir(profilePath);

    const newProfile: ProfileData = {
      connectorName,
      profilePath,
      createdAt: new Date(),
      lastUsed: new Date(),
      isActive: true,
      metadata: {}
    };

    this.activeProfiles.set(connectorName, newProfile);
    await this.saveProfileMetadata(newProfile);

    this.logger.info(`Created new browser profile for connector: ${connectorName}`);
    return newProfile;
  }

  /**
   * Save profile metadata to disk
   */
  private async saveProfileMetadata(profile: ProfileData): Promise<void> {
    try {
      const metadataPath = this.getProfileMetadataPath(profile.connectorName);
      await fs.writeJson(metadataPath, profile, { spaces: 2 });
      this.logger.debug(`Profile metadata saved for ${profile.connectorName}`);
    } catch (error) {
      this.logger.error(`Failed to save profile metadata for ${profile.connectorName}: ${error}`);
      throw error;
    }
  }

  /**
   * Load profile metadata from disk
   */
  private async loadProfileMetadata(connectorName: string): Promise<ProfileData | null> {
    try {
      const metadataPath = this.getProfileMetadataPath(connectorName);

      if (!await fs.pathExists(metadataPath)) {
        return null;
      }

      const profileData = await fs.readJson(metadataPath) as ProfileData;

      // Convert date strings back to Date objects
      profileData.createdAt = new Date(profileData.createdAt);
      profileData.lastUsed = new Date(profileData.lastUsed);

      this.logger.debug(`Profile metadata loaded for ${connectorName}`);
      return profileData;
    } catch (error) {
      this.logger.error(`Failed to load profile metadata for ${connectorName}: ${error}`);
      return null;
    }
  }

  /**
   * Mark a profile as inactive
   */
  async deactivateProfile(connectorName: string): Promise<void> {
    const profile = this.activeProfiles.get(connectorName);
    if (profile) {
      profile.isActive = false;
      await this.saveProfileMetadata(profile);
      this.activeProfiles.delete(connectorName);
      this.logger.debug(`Deactivated profile for ${connectorName}`);
    }
  }

  /**
   * Delete a profile completely
   */
  async deleteProfile(connectorName: string): Promise<void> {
    try {
      const profilePath = this.getProfilePath(connectorName);
      const metadataPath = this.getProfileMetadataPath(connectorName);

      // Remove from active profiles
      this.activeProfiles.delete(connectorName);

      // Delete profile directory
      if (await fs.pathExists(profilePath)) {
        await fs.remove(profilePath);
      }

      // Delete metadata file
      if (await fs.pathExists(metadataPath)) {
        await fs.unlink(metadataPath);
      }

      this.logger.info(`Profile deleted for connector: ${connectorName}`);
    } catch (error) {
      this.logger.error(`Failed to delete profile for ${connectorName}: ${error}`);
      throw error;
    }
  }

  /**
   * List all available profiles
   */
  async listProfiles(): Promise<ProfileData[]> {
    try {
      const files = await fs.readdir(this.profilesDir);
      const profileFiles = files.filter(file => file.endsWith('-profile.json'));

      const profiles: ProfileData[] = [];

      for (const file of profileFiles) {
        const connectorName = file.replace('-profile.json', '');
        const profile = await this.loadProfileMetadata(connectorName);
        if (profile) {
          profiles.push(profile);
        }
      }

      return profiles.sort((a, b) => b.lastUsed.getTime() - a.lastUsed.getTime());
    } catch (error) {
      this.logger.error(`Failed to list profiles: ${error}`);
      return [];
    }
  }

  /**
   * Clean up old or unused profiles
   */
  async cleanupProfiles(maxAge: number = 30 * 24 * 60 * 60 * 1000): Promise<void> {
    try {
      const profiles = await this.listProfiles();
      const now = Date.now();

      for (const profile of profiles) {
        const age = now - profile.lastUsed.getTime();

        if (age > maxAge && !profile.isActive) {
          this.logger.info(`Cleaning up old profile for ${profile.connectorName} (${Math.floor(age / (24 * 60 * 60 * 1000))} days old)`);
          await this.deleteProfile(profile.connectorName);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to cleanup profiles: ${error}`);
    }
  }

  /**
   * Get profile statistics
   */
  async getProfileStats(): Promise<{
    totalProfiles: number;
    activeProfiles: number;
    totalSize: number;
  }> {
    try {
      const profiles = await this.listProfiles();
      let totalSize = 0;

      for (const profile of profiles) {
        if (await fs.pathExists(profile.profilePath)) {
          const stats = await this.getDirectorySize(profile.profilePath);
          totalSize += stats;
        }
      }

      return {
        totalProfiles: profiles.length,
        activeProfiles: profiles.filter(p => p.isActive).length,
        totalSize
      };
    } catch (error) {
      this.logger.error(`Failed to get profile stats: ${error}`);
      return { totalProfiles: 0, activeProfiles: 0, totalSize: 0 };
    }
  }

  /**
   * Calculate directory size recursively
   */
  private async getDirectorySize(dirPath: string): Promise<number> {
    let size = 0;

    try {
      const items = await fs.readdir(dirPath);

      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stat = await fs.stat(itemPath);

        if (stat.isDirectory()) {
          size += await this.getDirectorySize(itemPath);
        } else {
          size += stat.size;
        }
      }
    } catch {
      // Ignore errors for individual files/directories
    }

    return size;
  }
}
