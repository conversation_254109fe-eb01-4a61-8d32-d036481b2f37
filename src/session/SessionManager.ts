import * as fs from 'fs-extra';
import * as path from 'path';
import { SessionData } from '../types';
import { Logger } from 'winston';

export class SessionManager {
  private sessionDir: string;
  private logger: Logger;

  constructor(sessionDir: string, logger: Logger) {
    this.sessionDir = sessionDir;
    this.logger = logger;
    this.ensureSessionDirectory();
  }

  private ensureSessionDirectory(): void {
    if (!fs.existsSync(this.sessionDir)) {
      fs.mkdirSync(this.sessionDir, { recursive: true });
      this.logger.debug(`Created session directory: ${this.sessionDir}`);
    }
  }

  private getSessionFilePath(serviceName: string): string {
    return path.join(this.sessionDir, `${serviceName}-session.json`);
  }

  async saveSession(serviceName: string, sessionData: SessionData): Promise<void> {
    try {
      const filePath = this.getSessionFilePath(serviceName);
      await fs.writeJson(filePath, sessionData, { spaces: 2 });
      this.logger.info(`Session saved for service: ${serviceName}`);
    } catch (error) {
      this.logger.error(`Failed to save session for ${serviceName}: ${error}`);
      throw error;
    }
  }

  async loadSession(serviceName: string): Promise<SessionData | null> {
    try {
      const filePath = this.getSessionFilePath(serviceName);

      if (!await fs.pathExists(filePath)) {
        this.logger.debug(`No session file found for service: ${serviceName}`);
        return null;
      }

      const sessionData = await fs.readJson(filePath) as SessionData;

      // Convert timestamp string back to Date object
      sessionData.timestamp = new Date(sessionData.timestamp);
      if (sessionData.expiresAt) {
        sessionData.expiresAt = new Date(sessionData.expiresAt);
      }

      this.logger.info(`Session loaded for service: ${serviceName}`);
      return sessionData;
    } catch (error) {
      this.logger.error(`Failed to load session for ${serviceName}: ${error}`);
      return null;
    }
  }

  async clearSession(serviceName: string): Promise<void> {
    try {
      const filePath = this.getSessionFilePath(serviceName);

      if (await fs.pathExists(filePath)) {
        await fs.unlink(filePath);
        this.logger.info(`Session cleared for service: ${serviceName}`);
      }
    } catch (error) {
      this.logger.error(`Failed to clear session for ${serviceName}: ${error}`);
      throw error;
    }
  }

  async isSessionValid(serviceName: string): Promise<boolean> {
    try {
      const sessionData = await this.loadSession(serviceName);

      if (!sessionData) {
        return false;
      }

      // Check if session has expired
      if (sessionData.expiresAt && sessionData.expiresAt < new Date()) {
        this.logger.debug(`Session expired for service: ${serviceName}`);
        await this.clearSession(serviceName);
        return false;
      }

      // Check if session is too old (default: 7 days)
      const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
      const age = Date.now() - sessionData.timestamp.getTime();

      if (age > maxAge) {
        this.logger.debug(`Session too old for service: ${serviceName}`);
        await this.clearSession(serviceName);
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Error checking session validity for ${serviceName}: ${error}`);
      return false;
    }
  }

  async listSessions(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.sessionDir);
      return files
        .filter(file => file.endsWith('-session.json'))
        .map(file => file.replace('-session.json', ''));
    } catch (error) {
      this.logger.error(`Failed to list sessions: ${error}`);
      return [];
    }
  }
}
