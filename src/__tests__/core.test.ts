import { TomodataCore } from '../core/TomodataCore';
import { createLogger } from '../utils/logger';

describe('TomodataCore', () => {
  let core: TomodataCore;

  beforeEach(() => {
    const logger = createLogger('./test-data');
    core = new TomodataCore(logger, './test-data');
  });

  test('should list available services', async () => {
    const services = await core.listServices();

    expect(services).toBeDefined();
    expect(services.length).toBeGreaterThan(0);
    expect(services[0]).toHaveProperty('name');
    expect(services[0]).toHaveProperty('displayName');
    expect(services[0]).toHaveProperty('enabled');
  });

  test('should get service info for ameli', async () => {
    const info = await core.getServiceInfo('ameli');

    expect(info).toBeDefined();
    expect(info.name).toBe('ameli');
    expect(info.displayName).toBe('Assurance Maladie');
    expect(info.baseUrl).toBe('https://www.ameli.fr');
  });

  test('should throw error for unknown service', async () => {
    await expect(core.getServiceInfo('unknown')).rejects.toThrow('Unknown service: unknown');
  });
});
