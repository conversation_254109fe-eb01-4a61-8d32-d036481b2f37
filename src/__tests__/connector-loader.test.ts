import { ConnectorLoader } from '../core/ConnectorLoader';
import { createLogger } from 'winston';
import * as path from 'path';

describe('ConnectorLoader', () => {
  let logger: ReturnType<typeof createLogger>;
  let connectorLoader: ConnectorLoader;

  beforeEach(() => {
    logger = createLogger({
      level: 'error', // Reduce logging during tests
      silent: true
    });

    // Use the actual connectors directory for testing
    const connectorsPath = path.join(__dirname, '../connectors');
    connectorLoader = new ConnectorLoader(logger, connectorsPath);
  });

  describe('loadConnectors', () => {
    it('should automatically discover and load available connectors', async () => {
      const connectors = await connectorLoader.loadConnectors();

      // Should have loaded at least the built-in connectors
      expect(connectors.size).toBeGreaterThan(0);

      // Check if specific connectors are loaded
      expect(connectors.has('ameli')).toBe(true);
      expect(connectors.has('template')).toBe(true);
    });

    it('should create working connector instances', async () => {
      const connectors = await connectorLoader.loadConnectors();

      const ameliFactory = connectors.get('ameli');
      expect(ameliFactory).toBeDefined();

      if (ameliFactory) {
        const ameliConnector = ameliFactory();
        expect(ameliConnector.name).toBe('ameli');
        expect(ameliConnector.displayName).toBe('Assurance Maladie');
        expect(typeof ameliConnector.initialize).toBe('function');
        expect(typeof ameliConnector.authenticate).toBe('function');
        expect(typeof ameliConnector.extractData).toBe('function');
        expect(typeof ameliConnector.cleanup).toBe('function');
      }
    });

    it('should ignore the base directory', async () => {
      const connectors = await connectorLoader.loadConnectors();

      // Should not include 'base' as a connector
      expect(connectors.has('base')).toBe(false);
    });

    it('should handle non-existent directories gracefully', async () => {
      const invalidLoader = new ConnectorLoader(logger, '/non/existent/path');

      const connectors = await invalidLoader.loadConnectors();
      expect(connectors.size).toBe(0);
    });
  });
});
