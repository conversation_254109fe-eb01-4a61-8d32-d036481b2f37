import { SecurityManager } from '../security/SecurityManager';
import { createLogger } from '../utils/logger';

describe('SecurityManager', () => {
  let securityManager: SecurityManager;

  beforeAll(() => {
    const logger = createLogger();
    securityManager = new SecurityManager(logger);
  });

  test('should encrypt and decrypt data successfully', async () => {
    const originalText = 'This is a top secret message!';
    const masterPassword = 'super-strong-password';

    const encryptedData = await securityManager.encrypt(originalText, masterPassword);
    expect(encryptedData).toBeDefined();
    expect(typeof encryptedData).toBe('string');
    expect(encryptedData).not.toBe(originalText);

    const decryptedText = await securityManager.decrypt(encryptedData, masterPassword);
    expect(decryptedText).toBe(originalText);
  });

  test('should fail to decrypt with wrong password', async () => {
    const originalText = 'Another secret';
    const correctPassword = 'correct-password';
    const wrongPassword = 'wrong-password';

    const encryptedData = await securityManager.encrypt(originalText, correctPassword);

    await expect(securityManager.decrypt(encryptedData, wrongPassword)).rejects.toThrow();
  });

  test('should fail to decrypt tampered data', async () => {
    const originalText = 'Untampered data';
    const masterPassword = 'secure-password';

    const encryptedData = await securityManager.encrypt(originalText, masterPassword);
    
    // Tamper with the data
    const tamperedData = '0' + encryptedData.substring(1);

    await expect(securityManager.decrypt(tamperedData, masterPassword)).rejects.toThrow();
  });
}); 