import { DataStorageManager } from '../storage/DataStorageManager';
import * as fs from 'fs-extra';
import * as path from 'path';

const TEST_DIR = path.join(__dirname, '../../test-data/storage-test');
const DOCTYPE = 'test-doctype';
const FILE_PATH = (doctype = DOCTYPE) => path.join(TEST_DIR, doctype, `${doctype}.json`);

const baseOptions = {
  primaryKey: 'id',
  preserveHistory: false,
  metadata: {},
};

const initialData = [
  { id: 1, value: 'A', count: 10 },
  { id: 2, value: 'B', count: 20 },
];

const updatedData = [
  { id: 1, value: 'A2', count: 15 }, // updated
  { id: 3, value: 'C', count: 30 }, // new
];

describe('DataStorageManager merge strategies', () => {
  let manager: DataStorageManager;

  beforeAll(async () => {
    await fs.remove(TEST_DIR);
    await fs.ensureDir(TEST_DIR);
    manager = new DataStorageManager({
      type: 'json',
      options: { baseDirectory: TEST_DIR, prettyPrint: false }
    }, console as unknown as import("winston").Logger);
    await manager.initialize();
  });

  afterEach(async () => {
    await fs.remove(TEST_DIR);
    await fs.ensureDir(TEST_DIR);
  });

  it('replace: remplace tout le contenu', async () => {
    await manager.saveData(DOCTYPE, initialData, { ...baseOptions, mergeStrategy: 'replace' });
    await manager.saveData(DOCTYPE, updatedData, { ...baseOptions, mergeStrategy: 'replace' });
    const file = await fs.readJson(FILE_PATH());
    expect(file.data.items).toEqual(updatedData);
  });

  it('append: ajoute les nouveaux items à la suite', async () => {
    await manager.saveData(DOCTYPE, initialData, { ...baseOptions, mergeStrategy: 'replace' });
    await manager.saveData(DOCTYPE, updatedData, { ...baseOptions, mergeStrategy: 'append' });
    const file = await fs.readJson(FILE_PATH());
    expect(file.data.items).toEqual([...initialData, ...updatedData]);
  });

  it('merge: met à jour les items existants, ajoute les nouveaux, conserve les anciens', async () => {
    await manager.saveData(DOCTYPE, initialData, { ...baseOptions, mergeStrategy: 'replace' });
    await manager.saveData(DOCTYPE, updatedData, { ...baseOptions, mergeStrategy: 'merge' });
    const file = await fs.readJson(FILE_PATH());
    // id:1 mis à jour, id:2 conservé, id:3 ajouté
    expect(file.data.items).toEqual([
      { id: 1, value: 'A2', count: 15 },
      { id: 2, value: 'B', count: 20 },
      { id: 3, value: 'C', count: 30 },
    ]);
  });

  it('sync-preserve-nonnull: ne garde que les nouveaux, conserve les anciennes valeurs non-nulles', async () => {
    await manager.saveData(DOCTYPE, initialData, { ...baseOptions, mergeStrategy: 'replace' });
    // Simule un scraping qui perd la valeur de count pour id:1
    const partialUpdate = [
      { id: 1, value: null, count: null },
      { id: 3, value: 'C', count: 30 },
    ];
    await manager.saveData(DOCTYPE, partialUpdate, { ...baseOptions, mergeStrategy: 'sync-preserve-nonnull' });
    const file = await fs.readJson(FILE_PATH());
    // id:1 conserve ses anciennes valeurs non-nulles, id:3 ajouté, id:2 supprimé
    expect(file.data.items).toEqual([
      { id: 1, value: 'A', count: 10 },
      { id: 3, value: 'C', count: 30 },
    ]);
  });
}); 