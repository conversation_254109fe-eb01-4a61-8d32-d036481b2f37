import { AmeliConnector } from '../connectors/ameli/AmeliConnector';
import { Logger } from 'winston';
import * as winston from 'winston';

describe('Ameli Connector Configuration', () => {
  let logger: Logger;

  beforeEach(() => {
    logger = winston.createLogger({
      level: 'debug',
      transports: [new winston.transports.Console({ silent: true })]
    });
  });

  it('should load Ameli-specific configuration from config.json', () => {
    const connector = new AmeliConnector(logger);
    const config = connector.getConfiguration();

    // Should load the actual Ameli config.json values
    expect(config.name).toBe('ameli');
    expect(config.displayName).toBe('Assurance Maladie');
    expect(config.baseUrl).toBe('https://www.ameli.fr');

    // Check Ameli-specific selectors
    expect(config.selectors.loginButton).toBe('#kc-login');
    expect(config.selectors.documentsLink).toBe('a[href*=\'attestation\'], a[href*=\'documents\'], a:has-text(\'Mes attestations\')');
  });

  it('should have access to Ameli-specific extended configuration', () => {
    const connector = new AmeliConnector(logger);
    const config = connector.getConfiguration();

    // Check Ameli-specific navigation URLs
    expect((config as any).navigation).toBeDefined();
    expect((config as any).navigation.documentsUrl).toBe('/assure/documents');
    expect((config as any).navigation.attestationsUrl).toBe('/assure/mes-attestations');
    expect((config as any).navigation.compteUrl).toBe('/assure/compte-ameli');

    // Check Ameli-specific features
    expect((config as any).features).toBeDefined();
    expect((config as any).features.autoDownload).toBe(true);
    expect((config as any).features.sessionPersistence).toBe(true);
    expect((config as any).features.duplicateDetection).toBe(true);
  });

  it('should have correct file patterns and naming for Ameli', () => {
    const connector = new AmeliConnector(logger);
    const config = connector.getConfiguration();

    expect(config.downloadPatterns).toContain('*.pdf');
    expect(config.downloadPatterns).toContain('*.csv');
    expect(config.downloadPatterns).toContain('*.zip');
    expect(config.fileNaming).toBe('{date}_ameli_{type}_{reference}.{ext}');
  });
});
