import { ProfileManager } from '../session/ProfileManager';
import { createLogger } from '../utils/logger';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

describe('ProfileManager', () => {
  let profileManager: ProfileManager;
  let testDataDir: string;
  let logger: ReturnType<typeof createLogger>;

  beforeEach(async () => {
    // Create a temporary directory for tests
    testDataDir = await fs.mkdtemp(path.join(os.tmpdir(), 'tomodata-test-'));
    logger = createLogger();
    profileManager = new ProfileManager(testDataDir, logger);
  });

  afterEach(async () => {
    // Clean up test data
    await fs.remove(testDataDir);
  });

  test('should create a new profile for a connector', async () => {
    const connectorName = 'test-connector';
    const profile = await profileManager.getOrCreateProfile(connectorName);

    expect(profile).toBeDefined();
    expect(profile.connectorName).toBe(connectorName);
    expect(profile.isActive).toBe(true);
    expect(profile.createdAt).toBeInstanceOf(Date);
    expect(profile.lastUsed).toBeInstanceOf(Date);
    expect(await fs.pathExists(profile.profilePath)).toBe(true);
  });

  test('should reuse existing profile for the same connector', async () => {
    const connectorName = 'test-connector';

    // Create first profile
    const profile1 = await profileManager.getOrCreateProfile(connectorName);
    const originalCreatedAt = profile1.createdAt;

    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 10));

    // Get profile again
    const profile2 = await profileManager.getOrCreateProfile(connectorName);

    expect(profile2.connectorName).toBe(connectorName);
    expect(profile2.createdAt.getTime()).toBe(originalCreatedAt.getTime());
    expect(profile2.lastUsed.getTime()).toBeGreaterThan(originalCreatedAt.getTime());
  });

  test('should list all profiles', async () => {
    await profileManager.getOrCreateProfile('connector1');
    await profileManager.getOrCreateProfile('connector2');

    const profiles = await profileManager.listProfiles();

    expect(profiles).toHaveLength(2);
    expect(profiles.map(p => p.connectorName)).toContain('connector1');
    expect(profiles.map(p => p.connectorName)).toContain('connector2');
  });

  test('should delete a profile', async () => {
    const connectorName = 'test-connector';
    const profile = await profileManager.getOrCreateProfile(connectorName);

    expect(await fs.pathExists(profile.profilePath)).toBe(true);

    await profileManager.deleteProfile(connectorName);

    expect(await fs.pathExists(profile.profilePath)).toBe(false);

    const profiles = await profileManager.listProfiles();
    expect(profiles.find(p => p.connectorName === connectorName)).toBeUndefined();
  });

  test('should get profile statistics', async () => {
    await profileManager.getOrCreateProfile('connector1');
    await profileManager.getOrCreateProfile('connector2');

    const stats = await profileManager.getProfileStats();

    expect(stats.totalProfiles).toBe(2);
    expect(stats.activeProfiles).toBe(2);
    expect(stats.totalSize).toBeGreaterThanOrEqual(0);
  });

  test('should cleanup old profiles', async () => {
    const connectorName = 'old-connector';
    const profile = await profileManager.getOrCreateProfile(connectorName);

    // Simulate old profile by modifying lastUsed date
    profile.lastUsed = new Date(Date.now() - (31 * 24 * 60 * 60 * 1000)); // 31 days ago
    profile.isActive = false;

    // Save the modified metadata
    const metadataPath = path.join(testDataDir, 'profiles', `${connectorName}-profile.json`);
    await fs.writeJson(metadataPath, profile, { spaces: 2 });

    // Run cleanup (30 days max age)
    await profileManager.cleanupProfiles(30 * 24 * 60 * 60 * 1000);

    // Profile should be deleted
    const profiles = await profileManager.listProfiles();
    expect(profiles.find(p => p.connectorName === connectorName)).toBeUndefined();
  });
});
