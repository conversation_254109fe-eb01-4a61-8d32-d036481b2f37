import { TemplateConnector } from '../connectors/template/TemplateConnector';
import { BaseConnector } from '../connectors/base/BaseConnector';
import { DataExtractionResult } from '../types';
import { Credentials } from '../connectors/base/ConnectorInterface';
import { Logger } from 'winston';
import * as winston from 'winston';

// Mock connector for testing missing config.json
class NonExistentConnector extends BaseConnector {
  name = 'non-existent';
  displayName = 'Non Existent Connector';
  version = '1.0.0';

  constructor(logger: Logger) {
    super(logger);
  }

  async authenticate(_credentials?: Credentials): Promise<boolean> {
    // Mock implementation for testing
    return false;
  }

  async isAuthenticated(): Promise<boolean> {
    // Mock implementation for testing
    return false;
  }

  async extractData(): Promise<DataExtractionResult> {
    return {
      success: false,
      downloads: [],
      errors: [],
      metadata: {}
    };
  }
}

describe('Connector Configuration Loading', () => {
  let logger: Logger;

  beforeEach(() => {
    logger = winston.createLogger({
      level: 'debug',
      transports: [new winston.transports.Console({ silent: true })]
    });
  });

  it('should load configuration from config.json file', () => {
    const connector = new TemplateConnector(logger);
    const config = connector.getConfiguration();

    // Should load the actual config.json values, not the default ones
    expect(config.name).toBe('template');
    expect(config.displayName).toBe('Template Connector');
    expect(config.baseUrl).toBe('https://toscrape.com');

    // Check for values that are in config.json but not in the default
    expect(config.selectors.username).toBe('#username');
    expect(config.selectors.password).toBe('#password');
    expect(config.selectors.loginButton).toBe('input[type=\'submit\']');
  });

  it('should validate configuration properly', () => {
    const connector = new TemplateConnector(logger);
    const config = connector.getConfiguration();

    const isValid = connector.validateConfiguration(config);
    expect(isValid).toBe(true);
  });

  it('should have access to extended configuration properties from config.json', () => {
    const connector = new TemplateConnector(logger);
    const config = connector.getConfiguration();

    // These properties should come from config.json
    expect((config as any).navigation).toBeDefined();
    expect((config as any).navigation.documentsUrl).toBe('/assure/documents');
    expect((config as any).timeouts).toBeDefined();
    expect((config as any).timeouts.authentication).toBe(300000);
  });

  it('should throw a clear error when config.json does not exist', () => {
    const connector = new NonExistentConnector(logger);

    expect(() => {
      connector.getConfiguration();
    }).toThrow('Configuration file not found:');

    expect(() => {
      connector.getConfiguration();
    }).toThrow('Please create a config.json file for the non-existent connector');
  });
});
