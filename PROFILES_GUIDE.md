# Guide des Profils Navigateur - Tomodata

## Aperçu

Tomodata sauvegarde maintenant un profil navigateur complet et séparé pour chaque connecteur, au lieu de simplement sauvegarder les cookies et le localStorage. Cela offre plusieurs avantages :

- **Isolation complète** : Chaque connecteur a son propre profil navigateur avec ses propres paramètres, extensions, cache, etc.
- **Persistance totale** : Toutes les données de session sont conservées entre les exécutions
- **Meilleure sécurité** : Les données de chaque service restent isolées
- **Performance** : Plus besoin de restaurer manuellement les cookies à chaque démarrage

## Utilisation

### Commandes CLI mises à jour

#### Exécution avec profils (par défaut)
```bash
# Utilise le profil navigateur persistant pour le connecteur ameli
./bin/tomodata run ameli

# Mode debug avec profil
./bin/tomodata run ameli --debug
```

#### Exécution sans profils (ancien mode)
```bash
# Utilise le mode session temporaire (comme avant)
./bin/tomodata run ameli --no-profile
```

### Nouvelles commandes de gestion des profils

#### Lister les profils
```bash
./bin/tomodata list-profiles
```

#### Supprimer un profil spécifique
```bash
./bin/tomodata clear-profile ameli
```

#### Nettoyer les anciens profils
```bash
# Supprime les profils non utilisés depuis 30 jours (par défaut)
./bin/tomodata cleanup-profiles

# Supprime les profils non utilisés depuis 7 jours
./bin/tomodata cleanup-profiles --days 7
```

## Structure des profils

Les profils sont stockés dans :
```
tomodata-data/
├── profiles/
│   ├── ameli/              # Profil navigateur pour Ameli
│   │   ├── Default/        # Données Chrome/Chromium
│   │   └── ...
│   ├── ameli-profile.json  # Métadonnées du profil
│   ├── youtube/            # Profil navigateur pour YouTube
│   └── youtube-profile.json
└── sessions/               # Ancien système (encore utilisé en mode --no-profile)
```

## Avantages du nouveau système

### 1. **Authentification persistante**
- Plus besoin de se reconnecter à chaque exécution
- Les sessions restent actives même après redémarrage

### 2. **Paramètres navigateur conservés**
- Taille d'écran, zoom, paramètres d'accessibilité
- Extensions installées (si nécessaire)
- Préférences de langue et région

### 3. **Cache et performances**
- Les ressources web sont mises en cache
- Chargement plus rapide des pages lors des visites suivantes

### 4. **Isolation de sécurité**
- Chaque service a sa propre empreinte navigateur
- Pas de fuite de données entre services

## Configuration et personnalisation

### Dans votre code TypeScript

```typescript
import { TomodataCore } from './core/TomodataCore';

const core = new TomodataCore(logger);

// Exécution avec profils (recommandé)
await core.run('ameli', {
  useProfile: true,  // défaut
  debug: false,
  headless: true
});

// Exécution sans profils (mode legacy)
await core.run('ameli', {
  useProfile: false,
  debug: false,
  headless: true
});
```

### Gestion programmatique des profils

```typescript
// Lister les profils
const profiles = await core.listProfiles();

// Obtenir les statistiques
const stats = await core.getProfileStats();

// Nettoyer les anciens profils
await core.cleanupOldProfiles(30); // 30 jours

// Supprimer un profil spécifique
await core.clearProfile('ameli');
```

## Migration depuis l'ancien système

Le système de profils est activé par défaut. L'ancien système de sessions est toujours disponible :

1. **Nouvelle installation** : Utilise automatiquement les profils
2. **Installation existante** : Les sessions existantes continuent de fonctionner
3. **Migration graduelle** : Les profils seront créés lors de la première utilisation de chaque connecteur

## Résolution de problèmes

### Profil corrompu
```bash
# Supprimer et recréer le profil
./bin/tomodata clear-profile <service>
./bin/tomodata run <service>
```

### Espace disque
```bash
# Vérifier la taille des profils
./bin/tomodata list-profiles

# Nettoyer les anciens profils
./bin/tomodata cleanup-profiles --days 7
```

### Problèmes d'authentification
```bash
# Forcer la recréation du profil
./bin/tomodata clear-profile <service>

# Ou utiliser le mode legacy temporairement
./bin/tomodata run <service> --no-profile
```

## Sécurité et confidentialité

- Chaque profil est stocké localement sur votre machine
- Les profils contiennent des données sensibles (cookies, mots de passe sauvegardés)
- Assurez-vous que le répertoire `tomodata-data/profiles/` a des permissions appropriées
- Considérez le chiffrement du disque pour une sécurité renforcée

## Performance

- **Premier lancement** : Légèrement plus lent (création du profil)
- **Lancements suivants** : Plus rapide (réutilisation du profil)
- **Espace disque** : Les profils occupent plus d'espace (~50-200MB par profil selon l'usage)
- **Mémoire** : Consommation similaire au mode session
