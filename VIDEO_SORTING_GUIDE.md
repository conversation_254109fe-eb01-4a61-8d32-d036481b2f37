# Guide de Tri des Vidéos YouTube - Tomodata

## Vue d'ensemble

Le système de tri des vidéos YouTube de Tomodata utilise un algorithme de scoring personnalisable pour classer vos vidéos de "Watch Later" selon vos priorités. Il prend en compte plusieurs critères pour vous aider à décider quoi regarder en premier.

## 🆕 Nouvelles Fonctionnalités

### ✅ **Affichage des Dates de Publication**
- Chaque vidéo affiche maintenant sa date de publication relative (ex: "il y a 2j", "il y a 3 mois")
- Conversion automatique des dates relatives en format ISO lors de l'extraction
- Support bilingue (français/anglais) pour une meilleure compatibilité

### ✅ **Extraction YouTube Améliorée**
- Conversion automatique des dates relatives ("il y a 1h") en vraies dates ISO
- Meilleure gestion des fuseaux horaires
- Performance optimisée pour l'extraction de grandes playlists

## Critères de Scoring

### 1. **Vidéos Courtes** (< 10 minutes)
- **Bonus par défaut :** +50 points
- **Logique :** Les vidéos courtes sont plus faciles à regarder rapidement

### 2. **Vidéos Récentes** (< 1 jour) ✨ NOUVEAU
- **Bonus par défaut :** +50 points
- **Logique :** Les vidéos très récentes sont prioritaires pour suivre l'actualité et les tendances

### 3. **Vidéos Anciennes** (> 1 mois)
- **Bonus par défaut :** +30 à +60 points selon l'âge
- **Logique :** Les vidéos anciennes risquent d'être oubliées, mieux vaut les prioriser

### 4. **Nombre de Vues**
- **Formule :** log₁₀(vues) × 0.15
- **Logique :** Les vidéos populaires ont souvent plus de valeur, mais avec un effet logarithmique pour éviter que les vues très élevées dominent

### 5. **Durée** (pénalité)
- **Formule :** durée_minutes × 0.8 (soustrait du score)
- **Logique :** Plus une vidéo est longue, plus elle demande d'engagement

### 6. **Favorites**
- **Bonus par défaut :** +100 points
- **Logique :** Les vidéos marquées comme favorites sont prioritaires

## Utilisation

### Commandes de base

```bash
# Analyser votre watch later avec les paramètres par défaut
./bin/tomodata sort-videos

# Utiliser un fichier spécifique
./bin/tomodata sort-videos ./tomodata-data/youtube-watch-later-2024-01-15.json

# Afficher les 20 meilleures recommandations
./bin/tomodata sort-videos --top 20

# Afficher les statistiques détaillées
./bin/tomodata sort-videos --statistics

# Expliquer le calcul du score pour chaque vidéo
./bin/tomodata sort-videos --explain

# Exporter le classement en JSON
./bin/tomodata sort-videos --export json

# Exporter en CSV pour Excel/Sheets
./bin/tomodata sort-videos --export csv
```

### Gestion des favorites

```bash
# Marquer des vidéos comme favorites (utiliser les IDs des vidéos)
./bin/tomodata sort-videos --favorites "dQw4w9WgXcQ" "kJQP7kiw5Fk"

# Combiner avec d'autres options
./bin/tomodata sort-videos --favorites "dQw4w9WgXcQ" --top 15 --explain
```

### Personnalisation des poids

```bash
# Personnaliser les critères de scoring
./bin/tomodata sort-videos --weights '{"shortVideoBias": 60, "recentBonus": 80, "favoriteBonus": 120, "durationWeight": 0.5}'

# Prioriser encore plus les vidéos récentes
./bin/tomodata sort-videos --weights '{"recentBonus": 100}'

# Prioriser encore plus les vidéos courtes
./bin/tomodata sort-videos --weights '{"shortVideoBias": 80}'

# Réduire l'importance des vues
./bin/tomodata sort-videos --weights '{"viewsWeight": 0.05}'
```

### Utilitaires

```bash
# Lister tous les fichiers YouTube disponibles
./bin/tomodata sort-videos --find-files

# Mode interactif (en développement)
./bin/tomodata sort-videos --interactive
```

## Exemples de Résultats

### Analyse Standard avec Dates de Publication ✨ NOUVEAU
```
==================================================
🎬 ANALYSE DE VOTRE WATCH LATER
==================================================
📊 Total: 127 vidéos
⚡ Vidéos courtes (<10min): 43
🆕 Vidéos récentes (<1 jour): 8
📅 Vidéos anciennes (>1 mois): 78
⭐ Favorites: 5
👀 Vues élevées: 64

==================================================
🏆 TOP 10 RECOMMANDATIONS
==================================================

 1. ⭐ [142.3pts] Comment programmer en 5 minutes
    📺 CodeRapide
    ⏱️  8min • 👀 1,234,567 vues • 📅 il y a 2h
    🔗 https://youtube.com/watch?v=...

 2.   [139.7pts] Breaking: Nouvelle fonctionnalité JavaScript
    📺 DevActu
    ⏱️  12min • 👀 567,890 vues • 📅 il y a 8h
    🔗 https://youtube.com/watch?v=...

 3.   [89.7pts] Tutoriel Git avancé
    📺 DevMaster
    ⏱️  15min • 👀 567,890 vues • 📅 il y a 1 sem
    🔗 https://youtube.com/watch?v=...
```

### Avec Explication du Score
```bash
./bin/tomodata sort-videos --explain --top 3
```

```
 1. ⭐ [142.3pts] Comment programmer en 5 minutes
    📺 CodeRapide
    ⏱️  8min • 👀 1,234,567 vues • 📅 il y a 2h
    🔗 https://youtube.com/watch?v=...
    📝 Score total: 142.3 points
       📝 Détail du calcul:
         ⚡ Vidéo courte (<10min): +50 pts
         🆕 Vidéo récente (2.0h depuis publiée): +50 pts
         👀 Vues (1,234,567): +9.2 pts
         ⏱️ Pénalité durée (8.0min): -6.4 pts
         ⭐ Favorite: +100 pts
```

### Statistiques Détaillées
```bash
./bin/tomodata sort-videos --statistics
```

```
==================================================
📊 STATISTIQUES DÉTAILLÉES
==================================================
📈 Total des vidéos: 127
⏱️  Durée totale: 42:15:30
📊 Durée moyenne: 19:56
⚡ Vidéos courtes (<10min): 43 (33.9%)
🆕 Vidéos récentes (<1 jour): 8 (6.3%)
📅 Vidéos anciennes (>1 mois): 78 (61.4%)
⭐ Favorites: 5 (3.9%)
👀 Vues moyennes: 245,678
🔥 Plus populaire: "Viral TikTok Compilation" (12,345,678 vues)
⏱️  Plus courte: 2min • Plus longue: 180min
```

## Personnalisation Avancée

### Profils de Scoring

**Profil "Quick Win"** - Privilégier les vidéos courtes
```bash
./bin/tomodata sort-videos --weights '{"shortVideoBias": 100, "durationWeight": 1.2}'
```

**Profil "Breaking News"** - Privilégier les vidéos très récentes ✨ NOUVEAU
```bash
./bin/tomodata sort-videos --weights '{"recentBonus": 100, "shortVideoBias": 30}'
```

**Profil "Content Discovery"** - Privilégier les vidéos anciennes
```bash
./bin/tomodata sort-videos --weights '{"ageBias": 50, "shortVideoBias": 20}'
```

**Profil "Popular Content"** - Privilégier les vues
```bash
./bin/tomodata sort-videos --weights '{"viewsWeight": 0.3, "shortVideoBias": 30}'
```

**Profil "Balanced Recent"** - Équilibrer récent et ancien ✨ NOUVEAU
```bash
./bin/tomodata sort-videos --weights '{"recentBonus": 40, "ageBias": 40, "shortVideoBias": 40}'
```

### Automatisation

Créer un script pour analyser régulièrement :

```bash
#!/bin/bash
# auto-sort.sh

echo "🔄 Extraction des données YouTube..."
./bin/tomodata run youtube

echo "📊 Analyse et tri des vidéos..."
./bin/tomodata sort-videos --statistics --export json --top 20

echo "✅ Terminé ! Consultez les fichiers exportés."
```

## Conseils d'Utilisation

### 🎯 **Stratégie de Visionnage Optimale**

1. **Session Breaking News** : Commencez par les vidéos récentes (<1 jour) pour l'actualité
2. **Session Courte** : Regardez les 3-5 premières vidéos du classement
3. **Session Longue** : Mélangez vidéos courtes et une vidéo longue bien classée
4. **Découverte** : Explorez les vidéos anciennes bien notées

### 📅 **Lecture des Dates de Publication** ✨ AMÉLIORÉ

- **"il y a 1h" à "il y a 1j"** : Contenu très récent (+50 pts), idéal pour l'actualité
- **"il y a 1 sem" à "il y a 1 mois"** : Contenu récent mais pas urgent
- **"il y a 3 mois" et plus** : Contenu "evergreen", parfait pour l'apprentissage

### 📈 **Ajustement des Poids**

- **Trop de vidéos longues en tête ?** → Augmentez `durationWeight`
- **Pas assez de variété ?** → Réduisez `favoriteBonus`
- **Ignorer les vues ?** → Mettez `viewsWeight` à 0
- **Prioriser le contenu récent ?** → Augmentez `recentBonus`
- **Équilibrer récent/ancien ?** → Utilisez `recentBonus` et `ageBias` égaux

### 🔄 **Workflow Recommandé**

1. Extraire les données : `./bin/tomodata run youtube`
2. Analyser : `./bin/tomodata sort-videos --statistics`
3. Identifier les tendances par date de publication
4. Commencer par les vidéos récentes pour l'actualité
5. Marquer les favorites importantes
6. Exporter la liste : `--export csv`
7. Suivre vos préférences avec des poids personnalisés

## Export et Integration

### Format JSON ✨ AMÉLIORÉ
Le fichier exporté contient maintenant les dates de publication :
```json
[
  {
    "rank": 1,
    "title": "Titre de la vidéo",
    "channel": "Nom de la chaîne",
    "duration": "8:45",
    "views": "1,234,567",
    "priorityScore": 142.3,
    "url": "https://youtube.com/watch?v=...",
    "isFavorite": true,
    "extractedAt": "2024-01-15T10:30:00.000Z",
    "publishedDate": "2024-01-13T08:15:00.000Z",
    "publishedDateRelative": "il y a 2j"
  }
]
```

### Format CSV
Compatible avec Excel, Google Sheets, ou tout autre outil d'analyse.
Inclut maintenant les colonnes de dates pour faciliter l'analyse temporelle.

## Nouvelles Fonctionnalités Techniques

### 🔧 **Extraction YouTube Améliorée**

- **Conversion automatique des dates** : Les dates relatives comme "il y a 1h" sont automatiquement converties en format ISO
- **Support multilingue** : Reconnaissance des formats français et anglais
- **Gestion des fuseaux horaires** : Les dates sont correctement gérées selon votre fuseau horaire local
- **Performance optimisée** : Extraction en une seule passe pour de meilleures performances

### 📊 **Analyse Temporelle**

Utilisez les nouvelles données de dates pour :
- Identifier les tendances de vos ajouts à Watch Later
- Équilibrer contenu récent vs contenu "evergreen"
- Planifier vos sessions de visionnage selon l'actualité du contenu
- **Nouveau :** Prioriser automatiquement les vidéos de moins de 1 jour

## Troubleshooting

### Aucun fichier trouvé
```bash
# Vérifier les fichiers disponibles
./bin/tomodata sort-videos --find-files

# Extraire d'abord les données
./bin/tomodata run youtube
```

### Scoring incohérent
- Vérifiez vos poids personnalisés
- Utilisez `--explain` pour comprendre le calcul
- Testez avec les poids par défaut
- **Nouveau :** Ajustez `recentBonus` selon vos préférences d'actualité

### Dates non affichées
- Les dates sont extraites lors de l'extraction YouTube
- Pour les anciens fichiers, relancez `./bin/tomodata run youtube`
- Vérifiez que votre fichier contient les champs `publishedDate`

### Performance
- Le tri est optimisé même pour de grandes collections (1000+ vidéos)
- L'export peut prendre quelques secondes pour de très gros fichiers
- L'extraction des dates améliore légèrement le temps de traitement

---

**💡 Tip :** Commencez par l'analyse standard, puis ajustez progressivement selon vos préférences ! Le nouveau bonus pour les vidéos récentes vous aidera à rester à jour avec l'actualité tout en gardant un équilibre avec vos autres contenus.
