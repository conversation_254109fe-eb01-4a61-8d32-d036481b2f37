# Tomodata - Architecture System

## Vue d'ensemble de l'architecture

Tomodata suit une architecture modulaire et extensible permettant d'ajouter facilement de nouveaux connecteurs pour différents services. Le système est conçu autour d'un cœur central qui orchestre les différents modules.

## Architecture générale

```
┌─────────────────────────────────────────────────────────────┐
│                     TOMODATA CORE                          │
├─────────────────────────────────────────────────────────────┤
│  CLI Interface  │  Web Interface  │  Developer Mode        │
├─────────────────────────────────────────────────────────────┤
│              Session Manager                               │
├─────────────────────────────────────────────────────────────┤
│  Connector      │  File Manager   │  Security Manager      │
│  Manager        │                 │                        │
├─────────────────────────────────────────────────────────────┤
│  Storage Layer  │  Configuration  │  Scheduler             │
├─────────────────────────────────────────────────────────────┤
│              Playwright Automation Engine                  │
└─────────────────────────────────────────────────────────────┘
```

## Composants principaux

### 1. Core Engine (`src/core/`)

#### TomodataCore
Le composant central qui orchestre tous les autres modules.

```typescript
class TomodataCore {
  private sessionManager: SessionManager;
  private connectorManager: ConnectorManager;
  private fileManager: FileManager;
  private securityManager: SecurityManager;
  private scheduler: Scheduler;

  async run(serviceName: string, options: RunOptions): Promise<void>
  async listServices(): Promise<Service[]>
  async configure(config: Configuration): Promise<void>
}
```

### 2. Connector System (`src/connectors/`)

#### Architecture des connecteurs
Chaque connecteur est un module indépendant qui implémente l'interface `IConnector`.

```typescript
interface IConnector {
  name: string;
  displayName: string;
  version: string;

  // Lifecycle methods
  initialize(context: ConnectorContext): Promise<void>;
  authenticate(credentials?: Credentials): Promise<boolean>;
  extractData(): Promise<DataExtractionResult>;
  cleanup(): Promise<void>;

  // Configuration
  getConfiguration(): ConnectorConfiguration;
  validateConfiguration(config: ConnectorConfiguration): boolean;
}
```

#### Structure d'un connecteur
```
src/connectors/
├── base/
│   ├── BaseConnector.ts          # Classe de base
│   └── ConnectorInterface.ts     # Interface commune
├── ameli/
│   ├── AmeliConnector.ts         # Implémentation spécifique
│   ├── config.json              # Configuration du connecteur
│   └── selectors.json           # Sélecteurs CSS/XPath
└── registry.ts                  # Registre des connecteurs
```

### 3. Session Management (`src/session/`)

#### SessionManager
Gère les sessions utilisateur, cookies et localStorage.

```typescript
class SessionManager {
  async saveSession(serviceName: string, sessionData: SessionData): Promise<void>
  async loadSession(serviceName: string): Promise<SessionData | null>
  async clearSession(serviceName: string): Promise<void>
  async isSessionValid(serviceName: string): Promise<boolean>
}

interface SessionData {
  cookies: Cookie[];
  localStorage: Record<string, string>;
  sessionStorage: Record<string, string>;
  timestamp: Date;
  expiresAt?: Date;
}
```

### 4. File Management (`src/files/`)

#### FileManager
Gère le téléchargement, nommage et organisation des fichiers.

```typescript
class FileManager {
  async downloadFile(url: string, options: DownloadOptions): Promise<string>
  async organizeFile(filePath: string, rules: OrganizationRules): Promise<string>
  async generateFileName(metadata: FileMetadata, pattern: string): Promise<string>
  async checkDuplicates(filePath: string): Promise<boolean>
}

interface DownloadOptions {
  targetPath: string;
  namingPattern: string;
  overwriteExisting: boolean;
  validateIntegrity: boolean;
}
```

### 5. Security Management (`src/security/`)

#### SecurityManager
Gère le chiffrement des données sensibles et la sécurité.

```typescript
class SecurityManager {
  async encryptCredentials(credentials: Credentials, masterPassword: string): Promise<string>
  async decryptCredentials(encryptedData: string, masterPassword: string): Promise<Credentials>
  async generateSecureHash(data: string): Promise<string>
  async validateMasterPassword(password: string): Promise<boolean>
}
```

### 6. Configuration Management (`src/config/`)

#### Configuration centralisée
```typescript
interface TomodataConfiguration {
  user: UserConfiguration;
  services: Record<string, ServiceConfiguration>;
  global: GlobalConfiguration;
}

interface UserConfiguration {
  id: string;
  preferences: {
    downloadPath: string;
    autoRename: boolean;
    organizationRules: OrganizationRules;
  };
}

interface ServiceConfiguration {
  enabled: boolean;
  credentials?: EncryptedCredentials;
  lastRun?: Date;
  schedule?: ScheduleConfiguration;
  customRules?: CustomRules;
}
```

### 7. Scheduling System (`src/scheduler/`)

#### Scheduler
Gère l'exécution automatique et programmée.

```typescript
class Scheduler {
  async scheduleService(serviceName: string, schedule: ScheduleConfiguration): Promise<void>
  async unscheduleService(serviceName: string): Promise<void>
  async getScheduledServices(): Promise<ScheduledService[]>
  async runScheduledTasks(): Promise<void>
}
```

## Interfaces utilisateur

### 1. CLI Interface (`src/cli/`)

Interface en ligne de commande pour les utilisateurs avancés.

```bash
# Examples d'utilisation
tomodata run ameli
tomodata configure ameli --interactive
tomodata list services
tomodata schedule ameli --daily
tomodata dev ameli --debug
```

### 2. Web Interface (`src/web/`)

Interface web simple pour les utilisateurs novices.

```
src/web/
├── public/
├── views/
│   ├── dashboard.html
│   ├── configure-service.html
│   └── developer-mode.html
└── server.ts
```

### 3. Developer Mode (`src/dev/`)

Mode développeur avec API exposée dans le navigateur.

```typescript
// API exposed in browser console
window.tomodata = {
  download: (url: string, options?: DownloadOptions) => Promise<string>,
  saveCredentials: (service: string, credentials: Credentials) => Promise<void>,
  getCurrentPage: () => PageInfo,
  extractElements: (selector: string) => Element[],
  injectScript: (script: string) => Promise<any>
};
```

## Stockage des données

### Structure de fichiers

```
tomodata-data/
├── config/
│   ├── user.json                 # Configuration utilisateur
│   ├── services.json            # Configuration des services
│   └── credentials.encrypted    # Identifiants chiffrés
├── sessions/
│   ├── ameli-session.json       # Sessions par service
│   └── ...
├── downloads/
│   ├── ameli/
│   │   ├── 2024/
│   │   │   ├── documents/
│   │   │   └── attestations/
│   │   └── ...
│   └── ...
└── logs/
    ├── tomodata.log
    └── connectors/
```

### Base de données (optionnelle)

Pour les intégrations avec PocketBase ou Cozy Cloud :

```sql
-- Schema exemple pour PocketBase
CREATE TABLE services (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  enabled BOOLEAN DEFAULT true,
  last_run DATETIME,
  credentials TEXT, -- encrypted
  config JSON
);

CREATE TABLE downloads (
  id TEXT PRIMARY KEY,
  service_id TEXT REFERENCES services(id),
  original_name TEXT,
  stored_name TEXT,
  downloaded_at DATETIME,
  file_hash TEXT,
  metadata JSON
);
```

## Sécurité

### Chiffrement des données

```typescript
// Utilisation de crypto natif Node.js
const algorithm = 'aes-256-gcm';
const keyDerivation = 'pbkdf2';

class EncryptionService {
  private deriveKey(password: string, salt: Buffer): Buffer
  encrypt(data: string, password: string): EncryptedData
  decrypt(encryptedData: EncryptedData, password: string): string
}
```

### Isolation des processus

- Chaque connecteur s'exécute dans un contexte Playwright isolé
- Séparation des sessions par service
- Pas de partage de données entre connecteurs

### Validation et audit

```typescript
interface SecurityAudit {
  timestamp: Date;
  action: string;
  service: string;
  success: boolean;
  details?: string;
}
```

## Extensibilité

### Ajout de nouveaux connecteurs

1. Créer un nouveau dossier dans `src/connectors/`
2. Implémenter l'interface `IConnector`
3. Ajouter la configuration JSON
4. Enregistrer dans le registre des connecteurs

### Plugin System (futur)

```typescript
interface IPlugin {
  name: string;
  version: string;
  dependencies: string[];

  install(): Promise<void>;
  uninstall(): Promise<void>;
  activate(context: PluginContext): Promise<void>;
}
```

## Déploiement et distribution

### Structure de packaging

```
tomodata/
├── bin/
│   └── tomodata               # Executable CLI
├── lib/                       # Code transpilé
├── connectors/               # Connecteurs disponibles
├── package.json
└── README.md
```

### Installation

```bash
# Installation globale
npm install -g tomodata

# Installation locale pour développement
git clone https://github.com/user/tomodata
cd tomodata
npm install
npm run build
```

## Monitoring et observabilité

### Logging

```typescript
interface LogEntry {
  timestamp: Date;
  level: 'debug' | 'info' | 'warn' | 'error';
  component: string;
  service?: string;
  message: string;
  metadata?: Record<string, any>;
}
```

### Métriques

- Taux de succès par connecteur
- Temps d'exécution moyen
- Nombre de fichiers téléchargés
- Erreurs par type

### Health Checks

```typescript
interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  services: Record<string, ServiceHealth>;
  lastCheck: Date;
}
```

## Technologies et dépendances

### Dependencies principales

```json
{
  "dependencies": {
    "playwright": "^1.40.0",
    "commander": "^11.0.0",
    "express": "^4.18.0",
    "node-cron": "^3.0.0",
    "crypto": "built-in",
    "fs-extra": "^11.0.0",
    "winston": "^3.10.0"
  },
  "devDependencies": {
    "typescript": "^5.0.0",
    "@types/node": "^20.0.0",
    "jest": "^29.0.0",
    "eslint": "^8.50.0"
  }
}
```

### Technologies de build

- TypeScript pour le typage statique
- ESBuild pour la compilation rapide
- Jest pour les tests
- ESLint pour la qualité du code

Cette architecture permet une évolution progressive du projet, en commençant par un MVP simple et en ajoutant progressivement les fonctionnalités avancées.
