# Guide du Système de Stockage de Données Centralisé

## Vue d'ensemble

Le système tomodata dispose maintenant d'un système centralisé de sauvegarde de données qui permet aux connecteurs de sauvegarder leurs données sans gérer directement les fichiers. Ce système supporte plusieurs types de stockage et est entièrement configurable.

## Architecture du système

### Composants principaux

1. **DataStorageManager** - Gestionnaire central de stockage
2. **BaseConnector.saveData()** - Méthode unifiée pour les connecteurs
3. **Configuration globale** - Paramètres de stockage dans `tomodata-data/config/config.json`

### Types de stockage supportés

- **JSON** (implémenté) - Fichiers JSON structurés
- **SQLite** (planifié) - Base de données locale
- **PostgreSQL** (planifié) - Base de données distante
- **MySQL** (planifié) - Base de données distante

## Configuration

### Configuration globale

Dans le fichier `tomodata-data/config/config.json`, ajoutez la section `dataStorage` :

```json
{
  "user": {
    "id": "default-user",
    "preferences": {
      "downloadPath": "./tomodata-data/downloads",
      "autoRename": true
    }
  },
  "services": {},
  "global": {
    "logLevel": "info",
    "maxRetries": 3,
    "timeout": 30000,
    "dataStorage": {
      "type": "json",
      "options": {
        "baseDirectory": "./tomodata-data/downloads",
        "prettyPrint": true,
        "compression": false,
        "backupEnabled": false,
        "retentionDays": 365
      }
    }
  }
}
```

### Options de configuration

#### Pour le stockage JSON (`type: "json"`)

| Option | Type | Défaut | Description |
|--------|------|---------|-------------|
| `baseDirectory` | string | `"./tomodata-data/downloads"` | Répertoire de base pour les fichiers |
| `prettyPrint` | boolean | `true` | Formater le JSON avec indentation |
| `compression` | boolean | `false` | Compresser les fichiers (pas encore implémenté) |
| `backupEnabled` | boolean | `false` | Créer des sauvegardes automatiques |
| `retentionDays` | number | `365` | Nombre de jours de rétention |

#### Pour les bases de données (futur)

| Option | Type | Description |
|--------|------|-------------|
| `host` | string | Adresse du serveur |
| `port` | number | Port de connexion |
| `database` | string | Nom de la base de données |
| `username` | string | Nom d'utilisateur |
| `password` | string | Mot de passe |
| `ssl` | boolean | Utiliser SSL/TLS |

## Utilisation dans les connecteurs

### Méthode saveData()

Les connecteurs héritent automatiquement de la méthode `saveData()` de `BaseConnector` :

```typescript
protected async saveData(
  doctype: string, 
  data: unknown, 
  options: SaveDataOptions = {}
): Promise<SaveDataResult>
```

### Paramètres

- **doctype** (string) : Type de document/données (ex: "youtube-watch-later", "ameli-documents")
- **data** (unknown) : Les données à sauvegarder
- **options** (SaveDataOptions) : Options de sauvegarde

### Options disponibles

```typescript
interface SaveDataOptions {
  filename?: string;          // Nom de fichier personnalisé
  tableName?: string;         // Nom de table pour base de données
  metadata?: Record<string, unknown>; // Métadonnées supplémentaires
  overwrite?: boolean;        // Écraser si le fichier existe
  compress?: boolean;         // Compresser le fichier
  format?: 'json' | 'csv' | 'xml';   // Format de sortie
  schema?: Record<string, string>;   // Schéma pour base de données
  
  // Options de fusion de données
  primaryKey?: string | string[];     // Champ(s) clé primaire pour la fusion
  mergeStrategy?: 'replace' | 'merge' | 'append' | 'custom'; // Stratégie de fusion
  customMerger?: (existing: unknown, incoming: unknown) => unknown; // Fonction de fusion personnalisée
  updateTimestamp?: boolean;          // Mettre à jour l'horodatage lors de la fusion
  preserveHistory?: boolean;          // Conserver l'historique des modifications
}
```

### Résultat

```typescript
interface SaveDataResult {
  success: boolean;           // Succès de l'opération
  filePath?: string;          // Chemin du fichier créé
  tableId?: string;           // ID de table (base de données)
  recordId?: string | number; // ID d'enregistrement
  size: number;               // Taille en octets
  timestamp: string;          // Horodatage
  metadata?: Record<string, unknown>; // Métadonnées de retour
  error?: string;             // Message d'erreur si échec
  
  // Résultats des opérations de fusion
  operation?: 'create' | 'update' | 'merge'; // Type d'opération effectuée
  itemsProcessed?: number;    // Nombre d'éléments traités
  itemsCreated?: number;      // Nouveaux éléments créés
  itemsUpdated?: number;      // Éléments existants mis à jour
  mergeDetails?: MergeOperationDetails; // Détails de l'opération de fusion
}
```

## Exemples d'utilisation

### Exemple 1 : Sauvegarde avec fusion de données (YouTube)

```typescript
// Dans YoutubeConnector.extractData()
const playlistData = await this.extractWatchLaterData();

const saveResult = await this.saveData('youtube-watch-later', playlistData.videos, {
  primaryKey: 'videoId', // Utilise videoId comme clé primaire
  mergeStrategy: 'merge', // Fusionne les données existantes avec les nouvelles
  updateTimestamp: true,
  preserveHistory: true,
  metadata: {
    totalVideos: playlistData.videos.length,
    playlistTitle: playlistData.title,
    playlistExtractedAt: playlistData.extractedAt
  }
});

if (saveResult.success) {
  this.logger.info(`Données fusionnées : ${saveResult.itemsCreated} nouvelles, ${saveResult.itemsUpdated} mises à jour`);
}
```

### Exemple 2 : Sauvegarde avec nom personnalisé

```typescript
const documents = await this.extractDocuments();

const saveResult = await this.saveData('ameli-documents', documents, {
  filename: `attestations-${new Date().getFullYear()}.json`,
  metadata: {
    documentType: 'attestations',
    extractionDate: new Date().toISOString()
  }
});
```

### Exemple 3 : Sauvegarde avec gestion d'erreur

```typescript
try {
  const data = await this.extractData();
  
  const saveResult = await this.saveData('connector-data', data);
  
  if (!saveResult.success) {
    throw new Error(`Échec de sauvegarde : ${saveResult.error}`);
  }
  
  return {
    success: true,
    downloads: [{
      filename: path.basename(saveResult.filePath!),
      path: saveResult.filePath!,
      size: saveResult.size
    }],
    errors: [],
    metadata: { saveResult }
  };
} catch (error) {
  this.logger.error(`Erreur : ${error}`);
  return {
    success: false,
    downloads: [],
    errors: [error.message],
    metadata: {}
  };
}
```

## Fusion de données intelligente

### Principe

Le système de fusion permet d'éviter la duplication de données en utilisant des clés primaires pour identifier et fusionner les enregistrements existants. Plutôt que de créer un nouveau fichier à chaque extraction, le système met à jour les données existantes.

### Configuration de la fusion

#### Clé primaire

```typescript
// Clé simple
primaryKey: 'videoId'

// Clé composite
primaryKey: ['userId', 'videoId']
```

#### Stratégies de fusion

| Stratégie | Description | Exemple d'usage |
|-----------|-------------|-----------------|
| `replace` | Remplace complètement l'élément existant | Données où tout change |
| `merge` | Fusionne les propriétés (deep merge) | YouTube : garder historique mais MAJ vues/progression |
| `append` | Ajoute aux données existantes (tableaux) | Logs, historiques |
| `custom` | Fonction personnalisée de fusion | Logique métier complexe |

### Exemple concret : YouTube Watch Later

```typescript
// Structure d'une vidéo YouTube
interface VideoData {
  videoId: string;      // ← Clé primaire
  title: string;
  views: number;        // ← Mis à jour à chaque extraction
  progress: number;     // ← Mis à jour à chaque extraction
  addedDate: string;    // ← Conservé de la première extraction
  lastWatched?: string; // ← Mis à jour si nouvelle valeur
}

// Configuration de sauvegarde
const saveResult = await this.saveData('youtube-watch-later', videos, {
  primaryKey: 'videoId',
  mergeStrategy: 'merge',
  updateTimestamp: true,
  preserveHistory: true
});
```

### Comportement de fusion

1. **Première extraction** : Toutes les vidéos sont créées
2. **Extractions suivantes** :
   - Nouvelles vidéos → Ajoutées
   - Vidéos existantes → Propriétés fusionnées
   - Vidéos supprimées de la playlist → Conservées (pas supprimées)

### Avantages

- **Pas de duplication** : Un seul fichier par type de données
- **Mise à jour incrémentale** : Seules les modifications sont appliquées
- **Préservation des données** : Les anciennes données ne sont pas perdues
- **Historique** : Suivi des modifications dans le temps
- **Performance** : Traitement plus rapide des gros volumes

### Exemple de résultat de fusion

```typescript
{
  success: true,
  operation: 'merge',
  itemsProcessed: 150,
  itemsCreated: 5,      // 5 nouvelles vidéos
  itemsUpdated: 23,     // 23 vidéos avec vues/progression MAJ
  mergeDetails: {
    totalItems: 150,
    newItems: 5,
    updatedItems: 23,
    unchangedItems: 122,
    lastModified: "2024-01-15T10:30:00.000Z",
    mergeStrategy: "merge",
    primaryKeyFields: ["videoId"]
  }
}
```

## Structure des fichiers sauvegardés

### Stockage JSON

Les données sont organisées par type de document :

```
tomodata-data/downloads/
├── youtube/
│   └── youtube-watch-later.json          # ← Fichier unique avec fusion
├── ameli/
│   └── ameli-documents.json              # ← Fichier unique avec fusion
├── one-time-extractions/
│   ├── export-2024-01-15T10-30-00-000Z.json  # ← Sans clé primaire
│   └── backup-2024-01-16T14-22-30-000Z.json  # ← Sans clé primaire
└── custom-doctype/
    └── custom-doctype.json               # ← Fichier unique avec fusion
```

**Note** : Quand une `primaryKey` est définie, un fichier unique est utilisé avec fusion. Sans `primaryKey`, un nouveau fichier horodaté est créé à chaque fois.

### Format des fichiers JSON

```json
{
  "doctype": "youtube-watch-later",
  "extractedAt": "2024-01-15T10:30:00.000Z",
  "lastModified": "2024-01-16T14:22:30.000Z",
  "metadata": {
    "connectorName": "youtube",
    "connectorVersion": "1.0.0",
    "extractedBy": "YouTube",
    "primaryKeyFields": ["videoId"],
    "mergeStrategy": "merge",
    "totalVideos": 150,
    "playlistTitle": "À regarder plus tard",
    "playlistExtractedAt": "2024-01-16T14:22:30.000Z"
  },
  "data": {
    "items": [
      {
        "videoId": "abc123",
        "index": 1,
        "title": "Titre de la vidéo",
        "channel": "Nom de la chaîne",
        "views": 1250000,
        "progress": 45,
        "url": "https://youtube.com/watch?v=abc123",
        "extractedAt": "2024-01-16T14:22:30.000Z"
        // ... autres propriétés
      }
    ]
  },
  "history": [
    {
      "timestamp": "2024-01-15T10:30:00.000Z",
      "operation": "create",
      "itemsAffected": 145,
      "summary": "Initial data creation"
    },
    {
      "timestamp": "2024-01-16T14:22:30.000Z",
      "operation": "merge",
      "itemsAffected": 28,
      "summary": "Merged 5 new, 23 updated items"
    }
  ]
}
```

## Migration depuis l'ancien système

### Avant (méthode personnalisée)

```typescript
// Ancienne approche dans YoutubeConnector
private async savePlaylistToJSON(playlistData: PlaylistData): Promise<string> {
  const { downloadPath } = this.connectorContext;
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `youtube-watch-later-${timestamp}.json`;
  const filePath = path.join(downloadPath, filename);

  fs.writeFileSync(filePath, JSON.stringify(playlistData, null, 2), 'utf8');
  return filename;
}
```

### Après (méthode centralisée)

```typescript
// Nouvelle approche
const saveResult = await this.saveData('youtube-watch-later', playlistData, {
  metadata: {
    totalVideos: playlistData.videos.length,
    playlistTitle: playlistData.title
  }
});
```

## Avantages du nouveau système

1. **Centralisation** : Un seul endroit pour gérer tous les types de stockage
2. **Flexibilité** : Support de différents formats et destinations
3. **Métadonnées** : Enrichissement automatique avec les informations du connecteur
4. **Configuration** : Changement de type de stockage sans modifier le code
5. **Consistance** : Format uniforme pour tous les connecteurs
6. **Extensibilité** : Facile d'ajouter de nouveaux types de stockage

## Prochaines étapes

1. **Support SQLite** : Implémentation du stockage en base de données locale
2. **Support PostgreSQL/MySQL** : Stockage en base de données distante
3. **Compression** : Support de la compression automatique
4. **Sauvegardes** : Système de sauvegarde automatique
5. **Indexation** : Index de recherche dans les données sauvegardées
6. **API de requête** : Interface pour interroger les données stockées

## Compatibilité avec les outils existants

### Commande `sort-videos`

La commande `tomodata sort-videos` **utilise exclusivement le nouveau format de fusion** pour une meilleure simplicité et maintenance :

#### Utilisation

```bash
# Liste les fichiers disponibles
tomodata sort-videos --find-files
```

Sortie :
```
📁 Available YouTube data files:
  1. ./tomodata-data/downloads/youtube/youtube-watch-later.json
     Videos: 150 | Last modified: 2024-01-10T14:30:00.000Z
```

#### Commandes

```bash
# Utilise automatiquement le fichier le plus récent
tomodata sort-videos --top 20 --statistics

# Ou spécifie un fichier spécifique
tomodata sort-videos ./tomodata-data/downloads/youtube/youtube-watch-later.json --top 15
```

#### Format supporté

| Format | Localisation | Structure |
|--------|-------------|-----------|
| **Nouveau (fusion)** | `downloads/youtube/` | `data.items[]` |

### Migration depuis l'ancien format

❗ **Les anciens fichiers ne sont plus supportés**  
✅ **Exécutez `tomodata run youtube` pour générer le nouveau format**  
✅ **Recherche uniquement dans `downloads/youtube/`**

## Dépannage

### Erreur "Data storage manager not initialized"

Cette erreur indique que le connecteur n'a pas été correctement initialisé. Assurez-vous que :
1. La méthode `initialize()` a été appelée
2. La configuration globale est valide
3. Les permissions d'écriture sont disponibles

### Erreur de configuration

Vérifiez que le fichier `tomodata-data/config/config.json` contient une section `dataStorage` valide.

### Problèmes de permissions

Assurez-vous que l'application a les permissions d'écriture dans le répertoire de stockage configuré.

### Erreur "Unsupported data format" avec sort-videos

Si vous obtenez cette erreur :
1. Le fichier utilise l'ancien format qui n'est plus supporté
2. Exécutez `tomodata run youtube` pour générer un nouveau fichier
3. Utilisez `--find-files` pour lister les fichiers disponibles dans le nouveau format

### Erreur "No videos found" avec sort-videos

Si vous obtenez cette erreur :
1. Vérifiez que le fichier JSON est valide
2. Assurez-vous que le fichier contient `data.items[]` avec des vidéos
3. Utilisez `--find-files` pour lister les fichiers disponibles 