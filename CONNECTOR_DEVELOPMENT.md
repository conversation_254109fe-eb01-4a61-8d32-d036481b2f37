# Guide de développement des connecteurs

Ce guide explique comment créer et ajouter de nouveaux connecteurs à Tomodata en utilisant le système de chargement automatique.

## Chargement automatique des connecteurs

Tomodata utilise un système de chargement automatique qui découvre et charge tous les connecteurs présents dans le dossier `src/connectors/`. Il n'est plus nécessaire de déclarer manuellement les connecteurs dans le code principal.

### Comment cela fonctionne

1. **Découverte automatique** : Le système scanne tous les sous-dossiers de `src/connectors/` (excepté le dossier `base`)
2. **Chargement dynamique** : Pour chaque dossier trouvé, le système recherche le fichier principal du connecteur
3. **Validation** : Le connecteur est validé pour s'assurer qu'il implémente correctement l'interface `IConnector`
4. **Enregistrement** : Le connecteur est automatiquement ajouté au registre des connecteurs disponibles

## Créer un nouveau connecteur

### Structure de base

Pour créer un nouveau connecteur, suivez cette structure :

```
src/connectors/
└── mon-service/
    ├── MonServiceConnector.ts    # Fichier principal (obligatoire)
    ├── config.json             # Configuration du service (optionnel)
    └── README.md                # Documentation (recommandé)
```

### Conventions de nommage

Le système cherche automatiquement les fichiers suivants dans cet ordre de priorité :

1. `{NomDuDossier}Connector.ts` (ex: `AmeliConnector.ts`)
2. `{NomDuDossier}Connector.js`
3. `index.ts`
4. `index.js`

### Exemple de connecteur

```typescript
import { BaseConnector } from '../base/BaseConnector';
import { DataExtractionResult } from '../../types';
import { Logger } from 'winston';

export class MonServiceConnector extends BaseConnector {
  name = 'mon-service';                    // Identifiant unique
  displayName = 'Mon Service Web';         // Nom affiché à l'utilisateur
  version = '1.0.0';                       // Version du connecteur

  constructor(logger: Logger) {
    super(logger);
  }

  async extractData(): Promise<DataExtractionResult> {
    // Implémentation de l'extraction des données
    // ...

    return {
      success: true,
      downloads: [],
      errors: [],
      metadata: {}
    };
  }

  // Autres méthodes optionnelles...
}
```

### Interface requise

Tous les connecteurs doivent implémenter l'interface `IConnector` qui comprend :

```typescript
interface IConnector {
  // Propriétés d'identification
  name: string;
  displayName: string;
  version: string;

  // Méthodes du cycle de vie
  initialize(context: ConnectorContext): Promise<void>;
  authenticate(): Promise<boolean>;
  extractData(): Promise<DataExtractionResult>;
  cleanup(): Promise<void>;

  // Configuration
  getConfiguration(): ConnectorConfiguration;
}
```

## Ajouter un nouveau connecteur

### Étapes simples

1. **Créer le dossier** : Créez un nouveau dossier dans `src/connectors/` avec le nom de votre service
2. **Créer le fichier principal** : Implémentez votre connecteur en suivant les conventions
3. **Tester** : Votre connecteur sera automatiquement découvert et chargé
4. **Configurer** : Ajoutez un fichier `config.json` si nécessaire

### Exemple complet

Créons un connecteur pour un service fictif "MonBanque" :

```bash
# Structure des fichiers
src/connectors/
└── mon-banque/
    ├── MonBanqueConnector.ts
    ├── config.json
    └── README.md
```

**MonBanqueConnector.ts** :
```typescript
import { BaseConnector } from '../base/BaseConnector';
import { DataExtractionResult, ConnectorConfiguration } from '../../types';
import { Logger } from 'winston';

export class MonBanqueConnector extends BaseConnector {
  name = 'mon-banque';
  displayName = 'Ma Banque';
  version = '1.0.0';

  constructor(logger: Logger) {
    super(logger);
  }

  getConfiguration(): ConnectorConfiguration {
    return {
      name: this.name,
      displayName: this.displayName,
      baseUrl: 'https://www.ma-banque.fr',
      loginUrl: 'https://connexion.ma-banque.fr',
      selectors: {
        username: '#username',
        password: '#password',
        loginButton: '#login-button',
        downloadButton: '.download-btn'
      },
      downloadPatterns: ['*.pdf', '*.csv'],
      fileNaming: '{date}_{type}_{account}.{ext}'
    };
  }

  async extractData(): Promise<DataExtractionResult> {
    this.logger.info('Extracting data from Ma Banque...');

    // Implémentation spécifique...

    return {
      success: true,
      downloads: [],
      errors: [],
      metadata: {
        extractedAt: new Date(),
        accountsProcessed: 0
      }
    };
  }
}
```

**config.json** :
```json
{
  "name": "mon-banque",
  "displayName": "Ma Banque",
  "baseUrl": "https://www.ma-banque.fr",
  "loginUrl": "https://connexion.ma-banque.fr",
  "features": {
    "autoDownload": true,
    "smartNaming": true,
    "multiAccount": true
  },
  "selectors": {
    "username": "#username",
    "password": "#password",
    "loginButton": "#login-button",
    "documentsLink": "a[href*='documents']",
    "downloadButton": ".download-btn"
  },
  "navigation": {
    "documentsUrl": "/espace-client/documents"
  },
  "timeouts": {
    "navigation": 10000,
    "download": 30000
  }
}
```

### Test automatique

Une fois votre connecteur créé, vous pouvez vérifier qu'il est bien chargé :

```bash
# Lister tous les services disponibles
npm run start -- list

# Tester votre nouveau connecteur
npm run start -- run mon-banque --debug
```

## Bonnes pratiques

### 1. Nommage cohérent
- Utilisez des noms de dossier en minuscules avec des tirets
- Le nom du fichier principal doit suivre la convention `{NomDuDossier}Connector.ts`
- La classe doit être exportée avec le bon nom

### 2. Configuration externe
- Utilisez un fichier `config.json` pour les paramètres spécifiques
- Évitez de coder en dur les URLs et sélecteurs

### 3. Gestion sécurisée des identifiants

La classe `BaseConnector` fournit des méthodes pour gérer les identifiants de manière sécurisée. Vous n'avez pas besoin d'implémenter le chiffrement vous-même.

**Sauvegarder des identifiants**

Pour sauvegarder les identifiants d'un utilisateur, utilisez la méthode `saveCredentials`. Elle s'occupe du chiffrement en utilisant le mot de passe principal fourni par l'utilisateur.

```typescript
// Dans votre méthode authenticate() ou ailleurs
const credentials = { username: 'user', password: 'password123' };
await this.saveCredentials(credentials);
```

**Charger des identifiants**

Pour récupérer les identifiants chiffrés, utilisez la méthode `loadCredentials`.

```typescript
// Au début de votre méthode authenticate()
const credentials = await this.loadCredentials();
if (credentials) {
  // Utilisez les identifiants pour vous connecter
  await this.page.fill('#username', credentials.username);
  await this.page.fill('#password', credentials.password);
}
```

> **Note :** Ces méthodes nécessitent que l'utilisateur ait fourni un mot de passe principal au lancement de la commande `run`. Si ce n'est pas le cas, les identifiants ne seront ni sauvegardés ni chargés.

### 4. Gestion d'erreurs
- Implémentez une gestion d'erreurs robuste
- Utilisez le logger fourni pour tracer les actions
- Retournez des messages d'erreur explicites

### 5. Tests
- Créez des tests unitaires pour votre connecteur
- Testez les cas d'erreur et les situations dégradées

### 6. Documentation
- Ajoutez un README.md dans le dossier de votre connecteur
- Documentez les particularités et prérequis du service

## Dépannage

### Connecteur non découvert
- Vérifiez que le nom du fichier suit les conventions
- Assurez-vous que la classe est correctement exportée
- Consultez les logs pour voir les erreurs de chargement

### Erreurs d'interface
- Vérifiez que votre connecteur implémente toutes les méthodes requises
- Utilisez TypeScript pour avoir une validation au moment de la compilation

### Configuration non chargée
- Vérifiez la syntaxe du fichier `config.json`
- Assurez-vous que le fichier est dans le bon dossier

Le système de chargement automatique rend l'ajout de nouveaux connecteurs très simple et ne nécessite aucune modification du code principal de Tomodata.
