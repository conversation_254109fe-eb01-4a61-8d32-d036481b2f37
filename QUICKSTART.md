# Tomodata - Guide de démarrage rapide

## Installation

1. <PERSON><PERSON><PERSON> le projet et installez les dépendances :
```bash
git clone <repository-url>
cd tomodata
npm install
npx playwright install
npm run build
```

2. Ren<PERSON> le script exécutable :
```bash
chmod +x bin/tomodata
```

## Utilisation

### Lister les services disponibles
```bash
npm run dev list
# ou après build:
./bin/tomodata list
```

### Obtenir des informations sur un service
```bash
npm run dev info ameli
```

### Lancer l'extraction pour Ameli (mode développement)
```bash
# Mode visible (pour voir le navigateur) - recommandé pour la première utilisation
npm run dev run ameli --debug --headless false

# Mode normal (navigateur en arrière-plan)
npm run dev run ameli
```

> **IMPORTANT :** Lors du premier lancement d'un service nécessitant une authentification, il vous sera demandé de créer un **mot de passe principal (master password)**. Ce mot de passe sera utilisé pour chiffrer et déchiffrer tous vos identifiants de manière sécurisée. **Ne le perdez pas, il est essentiel pour accéder à vos données.**

### Commandes utiles

```bash
# Vider la session sauvegardée
npm run dev clear-session ameli

# Spécifier un dossier de téléchargement
npm run dev run ameli --path ./mes-documents
```

## Premier lancement avec Ameli

1. Lancez la commande avec le mode debug :
```bash
npm run dev run ameli --debug --headless false
```

2. **Saisie du mot de passe principal :** L'outil vous demandera votre mot de passe principal dans le terminal. Saisissez-le pour continuer.

3. Le navigateur s'ouvrira automatiquement sur la page de connexion d'Ameli

4. Connectez-vous manuellement avec vos identifiants

5. L'outil naviguera automatiquement vers la section des documents

6. Téléchargez manuellement les documents que vous souhaitez (pour cette version MVP)

7. Votre session sera automatiquement sauvegardée pour les prochaines utilisations

## Structure des fichiers créés

```
tomodata-data/
├── config/
│   └── config.json          # Configuration utilisateur
├── sessions/
│   └── ameli-session.json   # Session sauvegardée d'Ameli
├── downloads/
│   └── ameli/              # Documents téléchargés d'Ameli
└── logs/
    ├── tomodata.log        # Logs généraux
    └── error.log           # Logs d'erreurs
```

## Fonctionnalités de cette version (MVP)

- ✅ Navigation automatique vers le site d'Ameli
- ✅ Gestion des sessions (cookies sauvegardés)
- ✅ Assistance pour le téléchargement manuel
- ✅ Organisation basique des fichiers
- ✅ Interface en ligne de commande

## Prochaines étapes

Les prochaines versions incluront :
- Automatisation complète des téléchargements
- Chiffrement des identifiants
- Plus de connecteurs (autres services)
- Interface web simple
- Mode développeur avancé

## Dépannage

### Le navigateur ne s'ouvre pas
- Vérifiez que Playwright est installé : `npx playwright install`
- Essayez en mode debug : `--debug --headless false`

### Session expirée
- Supprimez la session : `npm run dev clear-session ameli`
- Relancez le processus

### Erreurs de permissions
- Vérifiez les permissions du dossier de téléchargement
- Essayez de spécifier un autre dossier avec `--path`

## Support

Pour signaler des problèmes ou contribuer, consultez le fichier TODO.md du projet.
