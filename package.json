{"name": "tomodata", "version": "0.1.0", "description": "Automated personal data extraction tool", "main": "dist/index.js", "bin": {"tomodata": "./bin/tomodata"}, "scripts": {"build": "tsc && npm run copy-assets", "copy-assets": "node -e \"const fs=require('fs-extra');const path=require('path');async function copy(){const srcDir='src/connectors';const distDir='dist/connectors';const dirs=await fs.readdir(srcDir,{withFileTypes:true});for(const dir of dirs){if(dir.isDirectory()&&dir.name!=='base'){const src=path.join(srcDir,dir.name);const dist=path.join(distDir,dir.name);await fs.ensureDir(dist);if(await fs.pathExists(path.join(src,'config.json'))){await fs.copy(path.join(src,'config.json'),path.join(dist,'config.json'));}const files=await fs.readdir(src);for(const file of files){if(file.endsWith('.js')){await fs.copy(path.join(src,file),path.join(dist,file));}}}}console.log('Assets copied!');}copy();\"", "dev": "ts-node src/index.ts", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "keywords": ["automation", "data", "privacy", "scraping"], "author": "Tomodata Team", "license": "MIT", "dependencies": {"express": "^4.18.0", "fs-extra": "^11.0.0", "minimist": "^1.2.8", "patchright": "^1.52.5", "prompts": "^2.4.2", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"@types/fs-extra": "^11.0.0", "@types/jest": "^29.0.0", "@types/minimist": "^1.2.5", "@types/node": "^20.0.0", "@types/prompts": "^2.4.9", "@types/uuid": "^9.0.0", "eslint": "^9.29.0", "jest": "^29.0.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.0", "typescript": "^5.0.0", "typescript-eslint": "^8.34.1"}, "engines": {"node": ">=18.0.0"}}