# Tomodata - TODO List

> **📍 État actuel :** Phase 1 MVP TERMINÉE ✅
> **🎯 Prochaine priorité :** Phase 2 - Security Manager et automatisation des identifiants
> **📅 Dernière mise à jour :** 18 juin 2025

## Phase 1: MVP (Version 0.1.0) - 4 semaines ✅ TERMINÉE

### Setup et infrastructure de base ⏱️ 1 semaine ✅

#### Configuration du projet ✅
- [x] Initialiser le projet Node.js avec TypeScript
- [x] Configurer ESLint, Prettier et les scripts de build
- [x] Créer la structure de dossiers selon l'architecture
- [x] Configurer Jest pour les tests unitaires
- [x] Installer et configurer Playwright
- [x] Créer le package.json avec les dépendances principales

#### Core Engine (basique) ✅
- [x] Créer la classe `TomodataCore` de base
- [x] Implémenter l'interface `IConnector`
- [x] Créer `BaseConnector` avec les méthodes communes
- [x] Implémenter le registre des connecteurs
- [x] Créer le système de configuration basique (JSON)

### Session Management ⏱️ 1 semaine ✅

#### SessionManager ✅
- [x] Implémenter la sauvegarde/chargement des cookies
- [x] Gérer le localStorage et sessionStorage
- [x] Créer le système de validation des sessions
- [x] Implémenter la persistance des sessions sur disque
- [x] Ajouter la gestion de l'expiration des sessions

### Premier connecteur (Ameli) ⏱️ 1.5 semaines ✅

#### Structure du connecteur Ameli ✅
- [x] Créer la configuration JSON pour ameli.fr
- [x] Identifier et documenter les sélecteurs CSS nécessaires
- [x] Implémenter `AmeliConnector` héritant de `BaseConnector`
- [x] Gérer la navigation vers la page de connexion
- [x] Implémenter la détection de succès/échec de connexion

#### Fonctionnalités de base ✅
- [x] Navigation assistée vers ameli.fr
- [x] Capture manuelle des identifiants (mode interactif)
- [x] Sauvegarde de session après connexion réussie
- [x] Réutilisation de session existante si valide

### File Management (basique) ⏱️ 0.5 semaines ✅

#### FileManager de base ✅
- [x] Implémenter le téléchargement de fichiers unique
- [x] Créer le système de nommage basique avec patterns
- [x] Implémenter la vérification de doublons simples
- [x] Créer la structure de dossiers de base

### Interface CLI basique ⏱️ 0.5 semaines ✅

#### CLI Interface ✅
- [x] Configurer Commander.js
- [x] Implémenter les commandes de base :
  - [x] `tomodata run <service>`
  - [x] `tomodata list`
  - [x] `tomodata info <service>`
  - [x] `tomodata clear-session <service>`
- [x] Ajouter l'aide et la documentation CLI

### Tests et validation ⏱️ 0.5 semaines ✅

- [x] Tests unitaires pour TomodataCore
- [x] Configuration Jest et ts-jest
- [x] Tests de base pour le système de connecteurs
- [x] Validation du workflow complet
- [x] Documentation d'installation et d'utilisation (README, QUICKSTART)

### 🎉 Bilan Phase 1 MVP - SUCCÈS COMPLET

**✅ Livrable principal :** Un outil fonctionnel pour la récupération assistée de données d'Ameli avec gestion de sessions.

**🚀 Fonctionnalités opérationnelles :**
- Architecture modulaire complète avec TypeScript
- Connecteur Ameli avec navigation automatique et assistance manuelle
- Gestion de sessions persistantes (cookies, localStorage)
- Interface CLI avec 4 commandes principales
- Système de logging avec Winston
- Tests unitaires et configuration de qualité de code
- Documentation utilisateur complète (README, QUICKSTART, ARCHITECTURE, PRD)
- Build system et packaging NPM

**📊 Métriques atteintes :**
- ✅ Compilation TypeScript sans erreurs
- ✅ Tests passant à 100%
- ✅ ESLint sans warnings
- ✅ Architecture extensible pour les prochaines phases

**🔄 Prêt pour la Phase 2 :** Chiffrement des identifiants et automatisation

---

## Phase 2: Automatisation et sécurité (Version 0.2.0) - 6 semaines

### Security Manager ⏱️ 2 semaines

#### Chiffrement des identifiants
- [x] Implémenter `SecurityManager` avec crypto Node.js
- [x] Créer le système de master password
- [x] Implémenter le chiffrement AES-256-GCM
- [x] Ajouter la dérivation de clé PBKDF2
- [x] Créer l'interface pour gérer les identifiants chiffrés

#### Intégration sécurisée
- [ ] Modifier les connecteurs pour utiliser les identifiants chiffrés
- [ ] Implémenter l'interception automatique des identifiants
- [ ] Ajouter la validation de la force des mots de passe
- [ ] Créer le système d'audit de sécurité

### Automatisation des téléchargements ⏱️ 2 semaines

#### Détection automatique
- [ ] Implémenter la détection de liens de téléchargement
- [ ] Créer le système de filtrage par type de fichier
- [ ] Ajouter la détection de nouveaux fichiers
- [ ] Implémenter la vérification d'intégrité des fichiers

#### Organisation avancée
- [ ] Créer les règles d'organisation par type de document
- [ ] Implémenter l'arborescence automatique (année/mois/type)
- [ ] Ajouter les métadonnées de fichiers
- [ ] Créer le système de renommage intelligent

### Connecteurs supplémentaires ⏱️ 1.5 semaines

#### Nouveaux services
- [ ] Rechercher et documenter 2-3 services populaires
- [ ] Créer le connecteur pour un service bancaire (ex: Crédit Agricole)
- [ ] Créer le connecteur pour un service de télécom (ex: Orange)
- [ ] Tester et valider les nouveaux connecteurs

### Interface web basique ⏱️ 0.5 semaines

#### Web Interface
- [ ] Créer un serveur Express.js basique
- [ ] Interface de dashboard simple (HTML/CSS/JS vanilla)
- [ ] Pages de configuration des services
- [ ] API REST pour les opérations de base

---

## Phase 3: Automatisation complète et mode développeur (Version 0.3.0) - 8 semaines

### Scheduler System ⏱️ 2 semaines

#### Planification
- [ ] Implémenter `Scheduler` avec node-cron
- [ ] Créer l'interface de configuration des horaires
- [ ] Ajouter la gestion des tâches en arrière-plan
- [ ] Implémenter la persistance des planifications
- [ ] Créer le système de notification/rapport d'exécution

### Mode développeur ⏱️ 3 semaines

#### API de développement
- [ ] Créer l'interface de développement dans le navigateur
- [ ] Implémenter l'injection d'API dans `window.tomodata`
- [ ] Ajouter les outils de débogage (logs, étapes)
- [ ] Créer l'interface de test des sélecteurs
- [ ] Implémenter la console interactive

#### Outils de développement de connecteurs
- [ ] Créer l'assistant de création de connecteurs
- [ ] Implémenter la validation automatique des sélecteurs
- [ ] Ajouter l'enregistrement/replay des actions
- [ ] Créer la documentation interactive

### Système de plugins ⏱️ 2 semaines

#### Plugin Architecture
- [ ] Définir l'interface `IPlugin`
- [ ] Créer le système de chargement dynamique
- [ ] Implémenter le gestionnaire de dépendances
- [ ] Ajouter la validation et la sécurité des plugins

### Monitoring et observabilité ⏱️ 1 semaine

#### Logging et métriques
- [ ] Intégrer Winston pour les logs structurés
- [ ] Créer le système de métriques
- [ ] Implémenter les health checks
- [ ] Ajouter le monitoring des performances
- [ ] Créer les tableaux de bord de monitoring

---

## Phase 4: Intégrations externes et optimisations (Version 0.4.0) - 4 semaines

### Intégrations Cozy Cloud ⏱️ 1.5 semaines

#### Cozy Cloud Storage
- [ ] Rechercher et comprendre l'API Cozy Cloud
- [ ] Implémenter l'adaptateur de stockage Cozy
- [ ] Créer l'authentification et la synchronisation
- [ ] Tester l'intégration complète

### Intégrations PocketBase ⏱️ 1.5 semaines

#### PocketBase Storage
- [ ] Étudier l'API PocketBase
- [ ] Créer les schémas de base de données
- [ ] Implémenter l'adaptateur PocketBase
- [ ] Ajouter la synchronisation bidirectionnelle

### Optimisations et stabilisation ⏱️ 1 semaine

#### Performance
- [ ] Optimiser les téléchargements parallèles
- [ ] Améliorer la gestion mémoire
- [ ] Optimiser le cache des sessions
- [ ] Profiler et optimiser les goulots d'étranglement

#### Stabilité
- [ ] Améliorer la gestion d'erreurs
- [ ] Ajouter la reprise sur erreur
- [ ] Implémenter les timeouts adaptatifs
- [ ] Créer les tests de charge

---

## Tâches transversales (tout au long du projet)

### Documentation
- [x] Maintenir le README.md à jour
- [x] Documenter l'API pour les développeurs (interfaces TypeScript)
- [x] Créer les guides d'utilisation (QUICKSTART.md)
- [x] Documenter les connecteurs existants (Ameli)
- [x] Créer les exemples et tutoriels (README et QUICKSTART)

### Tests
- [x] Configuration Jest et tests de base
- [ ] Maintenir la couverture de tests > 80%
- [ ] Tests d'intégration pour chaque connecteur
- [ ] Tests de sécurité (audit régulier)
- [ ] Tests de performance
- [ ] Tests end-to-end automatisés

### Qualité du code
- [x] Configuration ESLint et TypeScript strict
- [x] Respect des conventions TypeScript
- [x] Architecture modulaire et extensible
- [x] Mise à jour des dépendances (dépendances actuelles)
- [x] Analyse statique du code (ESLint + TypeScript)

### Conformité et légal
- [x] Documenter les aspects légaux (README)
- [x] Créer les disclaimers appropriés (MIT License)
- [ ] Vérifier la conformité RGPD
- [ ] Valider avec les CGU des services

---

## Backlog des améliorations futures

### Interface utilisateur
- [ ] Interface web moderne (React/Vue)
- [ ] Application desktop (Electron)
- [ ] Application mobile (React Native)
- [ ] Interface graphique pour la configuration

### Fonctionnalités avancées
- [ ] IA pour la détection automatique de contenu
- [ ] OCR pour l'extraction de données depuis les PDFs
- [ ] Analyse et catégorisation automatique des documents
- [ ] Export vers différents formats (Excel, CSV, etc.)

### Intégrations
- [ ] APIs bancaires (PSD2)
- [ ] Services cloud (Google Drive, Dropbox)
- [ ] Systèmes de gestion documentaire
- [ ] Outils de comptabilité

### Sécurité avancée
- [ ] Authentification à deux facteurs
- [ ] Intégration avec des gestionnaires de mots de passe
- [ ] Chiffrement end-to-end
- [ ] Audit de sécurité automatisé

---

## Notes importantes

### Priorisation
- Les phases sont organisées par ordre de valeur utilisateur
- Chaque phase doit être fonctionnelle et testable
- Les tâches peuvent être parallélisées selon les compétences disponibles

### Risques identifiés
- **Changements d'interface des services** : Nécessite une maintenance continue
- **Aspects légaux** : À valider régulièrement avec les CGU
- **Sécurité** : Audit de sécurité nécessaire avant chaque release majeure

### Métriques de succès
- Temps de mise en place < 5 minutes
- Taux de succès des téléchargements > 95%
- Temps de récupération des données réduit de 80%
- Couverture de tests > 80%
